import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import '../utils/permissions.dart';

/// 位置服务类
/// 处理GPS定位和位置相关功能
class LocationService {
  static LocationService? _instance;
  static LocationService get instance => _instance ??= LocationService._();
  
  LocationService._();
  
  /// 当前位置
  Position? _currentPosition;
  
  /// 位置流订阅
  StreamSubscription<Position>? _positionStreamSubscription;
  
  /// 位置更新回调
  Function(Position)? _onLocationUpdate;
  
  /// 获取当前位置
  Position? get currentPosition => _currentPosition;
  
  /// 初始化位置服务
  Future<bool> initialize() async {
    try {
      // 检查位置服务是否启用
      final serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('位置服务未启用');
        return false;
      }
      
      // 检查权限
      final hasPermission = await PermissionHelper.requestLocationPermission();
      if (!hasPermission) {
        debugPrint('位置权限未授予');
        return false;
      }
      
      // 获取当前位置
      await getCurrentLocation();
      
      debugPrint('位置服务初始化成功');
      return true;
    } catch (e) {
      debugPrint('位置服务初始化失败: $e');
      return false;
    }
  }
  
  /// 获取当前位置
  Future<Position?> getCurrentLocation() async {
    try {
      // 检查权限
      final hasPermission = await PermissionHelper.isLocationPermissionGranted();
      if (!hasPermission) {
        debugPrint('没有位置权限');
        return null;
      }
      
      // 获取位置
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );
      
      _currentPosition = position;
      debugPrint('获取位置成功: ${position.latitude}, ${position.longitude}');
      
      return position;
    } catch (e) {
      debugPrint('获取位置失败: $e');
      return null;
    }
  }
  
  /// 开始监听位置变化
  void startLocationUpdates({
    Function(Position)? onLocationUpdate,
    LocationAccuracy accuracy = LocationAccuracy.high,
    int distanceFilter = 10,
  }) {
    try {
      _onLocationUpdate = onLocationUpdate;
      
      const locationSettings = LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10,
      );
      
      _positionStreamSubscription = Geolocator.getPositionStream(
        locationSettings: locationSettings,
      ).listen(
        (Position position) {
          _currentPosition = position;
          _onLocationUpdate?.call(position);
          debugPrint('位置更新: ${position.latitude}, ${position.longitude}');
        },
        onError: (error) {
          debugPrint('位置更新错误: $error');
        },
      );
      
      debugPrint('开始监听位置变化');
    } catch (e) {
      debugPrint('开始位置监听失败: $e');
    }
  }
  
  /// 停止监听位置变化
  void stopLocationUpdates() {
    try {
      _positionStreamSubscription?.cancel();
      _positionStreamSubscription = null;
      _onLocationUpdate = null;
      debugPrint('停止监听位置变化');
    } catch (e) {
      debugPrint('停止位置监听失败: $e');
    }
  }
  
  /// 计算两点之间的距离（米）
  static double calculateDistance(
    double lat1,
    double lon1,
    double lat2,
    double lon2,
  ) {
    try {
      return Geolocator.distanceBetween(lat1, lon1, lat2, lon2);
    } catch (e) {
      debugPrint('计算距离失败: $e');
      return double.infinity;
    }
  }
  
  /// 检查是否在目标位置范围内
  bool isWithinRange(
    double targetLat,
    double targetLon,
    double radiusInMeters,
  ) {
    if (_currentPosition == null) {
      return false;
    }
    
    final distance = calculateDistance(
      _currentPosition!.latitude,
      _currentPosition!.longitude,
      targetLat,
      targetLon,
    );
    
    return distance <= radiusInMeters;
  }
  
  /// 获取位置精度描述
  String getAccuracyDescription(double accuracy) {
    if (accuracy <= 5) {
      return '高精度';
    } else if (accuracy <= 10) {
      return '中等精度';
    } else if (accuracy <= 20) {
      return '低精度';
    } else {
      return '精度较差';
    }
  }
  
  /// 格式化坐标显示
  static String formatCoordinates(double latitude, double longitude) {
    return '${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}';
  }
  
  /// 检查位置服务是否可用
  Future<bool> isLocationServiceAvailable() async {
    try {
      final serviceEnabled = await Geolocator.isLocationServiceEnabled();
      final hasPermission = await PermissionHelper.isLocationPermissionGranted();
      return serviceEnabled && hasPermission;
    } catch (e) {
      debugPrint('检查位置服务可用性失败: $e');
      return false;
    }
  }
  
  /// 清理资源
  void dispose() {
    stopLocationUpdates();
    _currentPosition = null;
    debugPrint('位置服务已清理');
  }
}
