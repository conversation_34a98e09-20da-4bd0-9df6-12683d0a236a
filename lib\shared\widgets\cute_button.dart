import 'package:flutter/material.dart';
import 'package:animate_do/animate_do.dart';
import '../../core/constants/app_colors.dart';

/// 可爱风格按钮组件
/// 符合二次元设计风格
class CuteButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final CuteButtonStyle style;
  final IconData? icon;
  final bool isLoading;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final double borderRadius;

  const CuteButton({
    super.key,
    required this.text,
    this.onPressed,
    this.style = CuteButtonStyle.primary,
    this.icon,
    this.isLoading = false,
    this.width,
    this.height,
    this.padding,
    this.borderRadius = 16.0,
  });

  @override
  State<CuteButton> createState() => _CuteButtonState();
}

class _CuteButtonState extends State<CuteButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            width: widget.width,
            height: widget.height ?? 48,
            decoration: BoxDecoration(
              gradient: _getGradient(),
              color: _getBackgroundColor(),
              borderRadius: BorderRadius.circular(widget.borderRadius),
              border: _getBorder(),
              boxShadow: _getShadow(),
            ),
            child: Material(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(widget.borderRadius),
              child: InkWell(
                onTap: widget.isLoading ? null : widget.onPressed,
                onTapDown: widget.onPressed != null
                    ? (_) => _animationController.forward()
                    : null,
                onTapUp: widget.onPressed != null
                    ? (_) => _animationController.reverse()
                    : null,
                onTapCancel: widget.onPressed != null
                    ? () => _animationController.reverse()
                    : null,
                borderRadius: BorderRadius.circular(widget.borderRadius),
                child: Container(
                  padding: widget.padding ??
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (widget.isLoading) ...[
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              _getTextColor(),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                      ] else if (widget.icon != null) ...[
                        Icon(
                          widget.icon,
                          color: _getTextColor(),
                          size: 18,
                        ),
                        const SizedBox(width: 8),
                      ],
                      Text(
                        widget.text,
                        style: TextStyle(
                          color: _getTextColor(),
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'PingFang SC',
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  LinearGradient? _getGradient() {
    switch (widget.style) {
      case CuteButtonStyle.primary:
        return AppColors.goldGradient;
      case CuteButtonStyle.purple:
        return AppColors.purpleGradient;
      case CuteButtonStyle.blue:
        return AppColors.blueGradient;
      case CuteButtonStyle.pink:
        return AppColors.pinkGradient;
      case CuteButtonStyle.outline:
      case CuteButtonStyle.text:
      case CuteButtonStyle.disabled:
        return null;
    }
  }

  Color? _getBackgroundColor() {
    switch (widget.style) {
      case CuteButtonStyle.primary:
      case CuteButtonStyle.purple:
      case CuteButtonStyle.blue:
      case CuteButtonStyle.pink:
        return null; // 使用渐变
      case CuteButtonStyle.outline:
      case CuteButtonStyle.text:
        return Colors.transparent;
      case CuteButtonStyle.disabled:
        return AppColors.textDisabled;
    }
  }

  Border? _getBorder() {
    switch (widget.style) {
      case CuteButtonStyle.outline:
        return Border.all(
          color: AppColors.primaryGold,
          width: 2,
        );
      default:
        return null;
    }
  }

  List<BoxShadow>? _getShadow() {
    if (widget.style == CuteButtonStyle.text ||
        widget.style == CuteButtonStyle.disabled) {
      return null;
    }
    
    return [
      BoxShadow(
        color: _getShadowColor(),
        blurRadius: 8,
        offset: const Offset(0, 4),
      ),
    ];
  }

  Color _getShadowColor() {
    switch (widget.style) {
      case CuteButtonStyle.primary:
        return AppColors.primaryGold.withOpacity(0.3);
      case CuteButtonStyle.purple:
        return AppColors.mysteryPurple.withOpacity(0.3);
      case CuteButtonStyle.blue:
        return AppColors.skyBlue.withOpacity(0.3);
      case CuteButtonStyle.pink:
        return AppColors.softPink.withOpacity(0.3);
      case CuteButtonStyle.outline:
        return AppColors.primaryGold.withOpacity(0.2);
      default:
        return Colors.transparent;
    }
  }

  Color _getTextColor() {
    switch (widget.style) {
      case CuteButtonStyle.primary:
      case CuteButtonStyle.purple:
      case CuteButtonStyle.blue:
      case CuteButtonStyle.pink:
        return Colors.white;
      case CuteButtonStyle.outline:
        return AppColors.primaryGold;
      case CuteButtonStyle.text:
        return AppColors.primaryOrange;
      case CuteButtonStyle.disabled:
        return Colors.white;
    }
  }
}

/// 可爱按钮样式枚举
enum CuteButtonStyle {
  /// 主要按钮 - 金色渐变
  primary,
  
  /// 紫色按钮 - 紫色渐变
  purple,
  
  /// 蓝色按钮 - 蓝色渐变
  blue,
  
  /// 粉色按钮 - 粉色渐变
  pink,
  
  /// 轮廓按钮 - 透明背景金色边框
  outline,
  
  /// 文本按钮 - 纯文本
  text,
  
  /// 禁用按钮
  disabled,
}

/// 浮动动作按钮
class CuteFloatingActionButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final IconData icon;
  final String? tooltip;
  final CuteButtonStyle style;

  const CuteFloatingActionButton({
    super.key,
    this.onPressed,
    required this.icon,
    this.tooltip,
    this.style = CuteButtonStyle.primary,
  });

  @override
  Widget build(BuildContext context) {
    return Pulse(
      infinite: true,
      duration: const Duration(seconds: 2),
      child: FloatingActionButton(
        onPressed: onPressed,
        tooltip: tooltip,
        backgroundColor: _getBackgroundColor(),
        foregroundColor: Colors.white,
        elevation: 8,
        child: Container(
          decoration: BoxDecoration(
            gradient: _getGradient(),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, size: 28),
        ),
      ),
    );
  }

  Color _getBackgroundColor() {
    switch (style) {
      case CuteButtonStyle.primary:
        return AppColors.primaryOrange;
      case CuteButtonStyle.purple:
        return AppColors.mysteryPurple;
      case CuteButtonStyle.blue:
        return AppColors.skyBlue;
      case CuteButtonStyle.pink:
        return AppColors.softPink;
      default:
        return AppColors.primaryOrange;
    }
  }

  LinearGradient? _getGradient() {
    switch (style) {
      case CuteButtonStyle.primary:
        return AppColors.goldGradient;
      case CuteButtonStyle.purple:
        return AppColors.purpleGradient;
      case CuteButtonStyle.blue:
        return AppColors.blueGradient;
      case CuteButtonStyle.pink:
        return AppColors.pinkGradient;
      default:
        return AppColors.goldGradient;
    }
  }
}

/// 图标按钮
class CuteIconButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final IconData icon;
  final CuteButtonStyle style;
  final double size;

  const CuteIconButton({
    super.key,
    this.onPressed,
    required this.icon,
    this.style = CuteButtonStyle.primary,
    this.size = 40,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        gradient: _getGradient(),
        borderRadius: BorderRadius.circular(size / 2),
        boxShadow: [
          BoxShadow(
            color: _getShadowColor(),
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(size / 2),
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(size / 2),
          child: Icon(
            icon,
            color: Colors.white,
            size: size * 0.5,
          ),
        ),
      ),
    );
  }

  LinearGradient _getGradient() {
    switch (style) {
      case CuteButtonStyle.primary:
        return AppColors.goldGradient;
      case CuteButtonStyle.purple:
        return AppColors.purpleGradient;
      case CuteButtonStyle.blue:
        return AppColors.blueGradient;
      case CuteButtonStyle.pink:
        return AppColors.pinkGradient;
      default:
        return AppColors.goldGradient;
    }
  }

  Color _getShadowColor() {
    switch (style) {
      case CuteButtonStyle.primary:
        return AppColors.primaryGold.withOpacity(0.3);
      case CuteButtonStyle.purple:
        return AppColors.mysteryPurple.withOpacity(0.3);
      case CuteButtonStyle.blue:
        return AppColors.skyBlue.withOpacity(0.3);
      case CuteButtonStyle.pink:
        return AppColors.softPink.withOpacity(0.3);
      default:
        return AppColors.primaryGold.withOpacity(0.3);
    }
  }
}
