import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/constants/app_colors.dart';
import '../../core/constants/app_strings.dart';
import '../../core/constants/app_sizes.dart';
import '../../providers/achievement_provider.dart';
import '../../models/achievement.dart';

/// 成就页面 - 显示成就收藏和进度
class AchievementsScreen extends StatefulWidget {
  const AchievementsScreen({super.key});

  @override
  State<AchievementsScreen> createState() => _AchievementsScreenState();
}

class _AchievementsScreenState extends State<AchievementsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeAchievements();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// 初始化成就数据
  Future<void> _initializeAchievements() async {
    final achievementProvider = context.read<AchievementProvider>();
    await achievementProvider.initializeAchievements();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.achievementsTitle),
        backgroundColor: AppColors.mysteryBlue,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppColors.treasureGold,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: '全部'),
            Tab(text: '已完成'),
            Tab(text: '进行中'),
          ],
        ),
      ),
      body: Consumer<AchievementProvider>(
        builder: (context, achievementProvider, child) {
          if (achievementProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (achievementProvider.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: AppSizes.iconXLarge,
                    color: AppColors.warningRed,
                  ),
                  const SizedBox(height: AppSizes.spacing16),
                  Text(
                    achievementProvider.errorMessage!,
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: AppSizes.spacing24),
                  ElevatedButton(
                    onPressed: _initializeAchievements,
                    child: const Text(AppStrings.buttonRetry),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // 成就统计
              _buildAchievementStats(achievementProvider),
              
              // 成就列表
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildAchievementList(achievementProvider.allAchievements),
                    _buildAchievementList(achievementProvider.completedAchievements),
                    _buildAchievementList(achievementProvider.inProgressAchievements),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// 构建成就统计
  Widget _buildAchievementStats(AchievementProvider provider) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.spacing16),
      color: AppColors.backgroundWhite,
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              '总成就',
              '${provider.allAchievements.length}',
              Icons.emoji_events,
              AppColors.mysteryBlue,
            ),
          ),
          const SizedBox(width: AppSizes.spacing8),
          Expanded(
            child: _buildStatCard(
              '已完成',
              '${provider.completedAchievements.length}',
              Icons.check_circle,
              AppColors.emeraldGreen,
            ),
          ),
          const SizedBox(width: AppSizes.spacing8),
          Expanded(
            child: _buildStatCard(
              '完成率',
              '${provider.completionPercentage.toStringAsFixed(1)}%',
              Icons.trending_up,
              AppColors.treasureGold,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建统计卡片
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.spacing12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
        border: Border.all(color: AppColors.borderGray),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: AppSizes.iconMedium),
          const SizedBox(height: AppSizes.spacing4),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
    );
  }

  /// 构建成就列表
  Widget _buildAchievementList(List<Achievement> achievements) {
    if (achievements.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.emoji_events_outlined,
              size: AppSizes.iconXLarge,
              color: AppColors.borderGray,
            ),
            SizedBox(height: AppSizes.spacing16),
            Text('暂无成就'),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppSizes.spacing8),
      itemCount: achievements.length,
      itemBuilder: (context, index) {
        final achievement = achievements[index];
        return _buildAchievementCard(achievement);
      },
    );
  }

  /// 构建成就卡片
  Widget _buildAchievementCard(Achievement achievement) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppSizes.spacing8,
        vertical: AppSizes.spacing4,
      ),
      child: InkWell(
        onTap: () => _showAchievementDetails(achievement),
        borderRadius: BorderRadius.circular(AppSizes.cardRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppSizes.cardPadding),
          child: Row(
            children: [
              // 成就图标
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: _getRarityColor(achievement.rarity).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                  border: Border.all(
                    color: _getRarityColor(achievement.rarity),
                    width: 2,
                  ),
                ),
                child: Icon(
                  _getAchievementIcon(achievement.type),
                  size: AppSizes.iconLarge,
                  color: achievement.isCompleted
                      ? _getRarityColor(achievement.rarity)
                      : AppColors.borderGray,
                ),
              ),
              const SizedBox(width: AppSizes.spacing16),
              
              // 成就信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题和稀有度
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            achievement.title,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: achievement.isCompleted
                                  ? AppColors.textBlack
                                  : AppColors.borderGray,
                            ),
                          ),
                        ),
                        _buildRarityBadge(achievement.rarity),
                      ],
                    ),
                    const SizedBox(height: AppSizes.spacing4),
                    
                    // 描述
                    Text(
                      achievement.description,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: achievement.isCompleted
                            ? AppColors.textBlack
                            : AppColors.borderGray,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: AppSizes.spacing8),
                    
                    // 进度条
                    if (!achievement.isCompleted) ...[
                      LinearProgressIndicator(
                        value: achievement.progressPercentage,
                        backgroundColor: AppColors.borderGray,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          _getRarityColor(achievement.rarity),
                        ),
                      ),
                      const SizedBox(height: AppSizes.spacing4),
                      Text(
                        '${achievement.currentProgress}/${achievement.targetProgress}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ] else ...[
                      Row(
                        children: [
                          const Icon(
                            Icons.check_circle,
                            size: AppSizes.iconSmall,
                            color: AppColors.emeraldGreen,
                          ),
                          const SizedBox(width: AppSizes.spacing4),
                          Text(
                            '已完成',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppColors.emeraldGreen,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            '+${achievement.experienceReward} EXP',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppColors.treasureGold,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建稀有度徽章
  Widget _buildRarityBadge(AchievementRarity rarity) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSizes.spacing8,
        vertical: AppSizes.spacing4,
      ),
      decoration: BoxDecoration(
        color: _getRarityColor(rarity),
        borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
      ),
      child: Text(
        rarity.displayName,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// 获取稀有度颜色
  Color _getRarityColor(AchievementRarity rarity) {
    switch (rarity) {
      case AchievementRarity.common:
        return Colors.grey;
      case AchievementRarity.rare:
        return Colors.blue;
      case AchievementRarity.epic:
        return Colors.purple;
      case AchievementRarity.legendary:
        return Colors.orange;
    }
  }

  /// 获取成就图标
  IconData _getAchievementIcon(AchievementType type) {
    switch (type) {
      case AchievementType.exploration:
        return Icons.explore;
      case AchievementType.collection:
        return Icons.collections;
      case AchievementType.social:
        return Icons.people;
      case AchievementType.challenge:
        return Icons.sports_esports;
      case AchievementType.special:
        return Icons.star;
    }
  }

  /// 显示成就详情
  void _showAchievementDetails(Achievement achievement) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(achievement.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(achievement.description),
            const SizedBox(height: AppSizes.spacing16),
            Text('类型: ${achievement.type.displayName}'),
            Text('稀有度: ${achievement.rarity.displayName}'),
            Text('经验奖励: ${achievement.experienceReward}'),
            if (!achievement.isCompleted) ...[
              const SizedBox(height: AppSizes.spacing8),
              Text('进度: ${achievement.currentProgress}/${achievement.targetProgress}'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}
