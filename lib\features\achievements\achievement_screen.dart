import 'package:flutter/material.dart';
import 'package:animate_do/animate_do.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_sizes.dart';
import '../../shared/widgets/cute_card.dart';
import '../../shared/widgets/cute_button.dart';

/// 成就收藏页面
/// 展示NFT成就卡片和收藏品
class AchievementScreen extends StatefulWidget {
  const AchievementScreen({super.key});

  @override
  State<AchievementScreen> createState() => _AchievementScreenState();
}

class _AchievementScreenState extends State<AchievementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int _selectedCategory = 0;

  final List<String> _categories = ['全部', '探险', '收集', '社交', '特殊'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _categories.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: AppColors.backgroundGradient,
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          title: const Text('🏆 成就收藏'),
          backgroundColor: Colors.transparent,
          actions: [
            IconButton(
              icon: const Icon(Icons.filter_list),
              onPressed: _showFilterDialog,
            ),
          ],
        ),
        body: Column(
          children: [
            // 成就统计
            _buildAchievementStats(),
            
            // 分类标签
            _buildCategoryTabs(),
            
            // 成就列表
            Expanded(
              child: _buildAchievementGrid(),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建成就统计
  Widget _buildAchievementStats() {
    return FadeInDown(
      duration: const Duration(milliseconds: 600),
      child: Container(
        margin: const EdgeInsets.all(AppSizes.spacing16),
        child: CuteCard(
          child: Row(
            children: [
              Expanded(
                child: _buildStatColumn('🏆', '15', '已获得'),
              ),
              Container(
                width: 1,
                height: 40,
                color: AppColors.border,
              ),
              Expanded(
                child: _buildStatColumn('📊', '68%', '完成度'),
              ),
              Container(
                width: 1,
                height: 40,
                color: AppColors.border,
              ),
              Expanded(
                child: _buildStatColumn('⭐', '3', '稀有成就'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建统计列
  Widget _buildStatColumn(String emoji, String value, String label) {
    return Column(
      children: [
        Text(emoji, style: const TextStyle(fontSize: 24)),
        const SizedBox(height: AppSizes.spacing4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primaryGold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  /// 构建分类标签
  Widget _buildCategoryTabs() {
    return FadeInLeft(
      duration: const Duration(milliseconds: 600),
      delay: const Duration(milliseconds: 200),
      child: Container(
        height: 50,
        margin: const EdgeInsets.symmetric(horizontal: AppSizes.spacing16),
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: _categories.length,
          itemBuilder: (context, index) {
            final isSelected = _selectedCategory == index;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedCategory = index;
                });
              },
              child: Container(
                margin: const EdgeInsets.only(right: AppSizes.spacing8),
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSizes.spacing16,
                  vertical: AppSizes.spacing8,
                ),
                decoration: BoxDecoration(
                  gradient: isSelected ? AppColors.goldGradient : null,
                  color: isSelected ? null : AppColors.cardWhite,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected ? Colors.transparent : AppColors.border,
                  ),
                  boxShadow: isSelected
                      ? [
                          BoxShadow(
                            color: AppColors.primaryGold.withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ]
                      : null,
                ),
                child: Center(
                  child: Text(
                    _categories[index],
                    style: TextStyle(
                      color: isSelected ? Colors.white : AppColors.textPrimary,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  /// 构建成就网格
  Widget _buildAchievementGrid() {
    return FadeInUp(
      duration: const Duration(milliseconds: 600),
      delay: const Duration(milliseconds: 400),
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.spacing16),
        child: GridView.builder(
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 0.8,
            crossAxisSpacing: AppSizes.spacing12,
            mainAxisSpacing: AppSizes.spacing12,
          ),
          itemCount: _getFilteredAchievements().length,
          itemBuilder: (context, index) {
            final achievement = _getFilteredAchievements()[index];
            return _buildAchievementCard(achievement, index);
          },
        ),
      ),
    );
  }

  /// 构建成就卡片
  Widget _buildAchievementCard(Map<String, dynamic> achievement, int index) {
    return FadeInUp(
      duration: const Duration(milliseconds: 600),
      delay: Duration(milliseconds: 100 * index),
      child: AchievementCard(
        title: achievement['title'],
        description: achievement['description'],
        imageUrl: achievement['imageUrl'],
        rarity: achievement['rarity'],
        isOwned: achievement['isOwned'],
        progress: achievement['progress'],
        onTap: () => _showAchievementDetail(achievement),
      ),
    );
  }

  /// 获取过滤后的成就
  List<Map<String, dynamic>> _getFilteredAchievements() {
    final allAchievements = [
      {
        'title': '初次探险',
        'description': '完成第一个探索任务',
        'imageUrl': 'https://example.com/achievement1.png',
        'rarity': 'common',
        'isOwned': true,
        'progress': 1.0,
        'category': '探险',
      },
      {
        'title': '咖啡爱好者',
        'description': '在10家不同咖啡店签到',
        'imageUrl': 'https://example.com/achievement2.png',
        'rarity': 'rare',
        'isOwned': true,
        'progress': 1.0,
        'category': '收集',
      },
      {
        'title': '摄影大师',
        'description': '拍摄100张高质量照片',
        'imageUrl': 'https://example.com/achievement3.png',
        'rarity': 'legendary',
        'isOwned': false,
        'progress': 0.65,
        'category': '收集',
      },
      {
        'title': '社交达人',
        'description': '与50位探险家成为好友',
        'imageUrl': 'https://example.com/achievement4.png',
        'rarity': 'rare',
        'isOwned': false,
        'progress': 0.3,
        'category': '社交',
      },
      {
        'title': '宝藏猎人',
        'description': '发现10个隐藏宝藏',
        'imageUrl': 'https://example.com/achievement5.png',
        'rarity': 'limited',
        'isOwned': false,
        'progress': 0.2,
        'category': '特殊',
      },
      {
        'title': '连续签到王',
        'description': '连续签到30天',
        'imageUrl': 'https://example.com/achievement6.png',
        'rarity': 'common',
        'isOwned': true,
        'progress': 1.0,
        'category': '探险',
      },
    ];

    if (_selectedCategory == 0) {
      return allAchievements;
    } else {
      final category = _categories[_selectedCategory];
      return allAchievements
          .where((achievement) => achievement['category'] == category)
          .toList();
    }
  }

  /// 显示成就详情
  void _showAchievementDetail(Map<String, dynamic> achievement) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: AppColors.cardWhite,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
        ),
        child: Column(
          children: [
            // 拖拽指示器
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: AppColors.border,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            // 成就详情内容
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(AppSizes.spacing20),
                child: Column(
                  children: [
                    // 成就图标和标题
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        gradient: _getAchievementGradient(achievement['rarity']),
                        borderRadius: BorderRadius.circular(24),
                        boxShadow: [
                          BoxShadow(
                            color: _getAchievementColor(achievement['rarity']).withOpacity(0.4),
                            blurRadius: 20,
                            offset: const Offset(0, 8),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.emoji_events,
                        size: 60,
                        color: Colors.white,
                      ),
                    ),
                    
                    const SizedBox(height: AppSizes.spacing24),
                    
                    Text(
                      achievement['title'],
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    
                    const SizedBox(height: AppSizes.spacing8),
                    
                    Text(
                      achievement['description'],
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    
                    const SizedBox(height: AppSizes.spacing24),
                    
                    // 进度条（如果未完成）
                    if (!achievement['isOwned']) ...[
                      Text(
                        '完成进度',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: AppSizes.spacing8),
                      
                      LinearProgressIndicator(
                        value: achievement['progress'],
                        backgroundColor: AppColors.border,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          _getAchievementColor(achievement['rarity']),
                        ),
                      ),
                      
                      const SizedBox(height: AppSizes.spacing8),
                      
                      Text(
                        '${(achievement['progress'] * 100).toInt()}% 完成',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                    
                    const Spacer(),
                    
                    // 操作按钮
                    if (achievement['isOwned'])
                      CuteButton(
                        text: '🎉 已获得成就',
                        onPressed: null,
                        style: CuteButtonStyle.disabled,
                      )
                    else
                      CuteButton(
                        text: '🎯 继续努力',
                        onPressed: () => Navigator.pop(context),
                        style: CuteButtonStyle.primary,
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示筛选对话框
  void _showFilterDialog() {
    // TODO: 实现筛选功能
  }

  /// 获取成就颜色
  Color _getAchievementColor(String rarity) {
    switch (rarity) {
      case 'common':
        return AppColors.rarityCommon;
      case 'rare':
        return AppColors.rarityRare;
      case 'legendary':
        return AppColors.rarityLegendary;
      case 'limited':
        return AppColors.rarityLimited;
      default:
        return AppColors.rarityCommon;
    }
  }

  /// 获取成就渐变
  LinearGradient _getAchievementGradient(String rarity) {
    switch (rarity) {
      case 'common':
        return LinearGradient(
          colors: [AppColors.rarityCommon, AppColors.rarityCommon.withOpacity(0.7)],
        );
      case 'rare':
        return AppColors.blueGradient;
      case 'legendary':
        return AppColors.purpleGradient;
      case 'limited':
        return AppColors.goldGradient;
      default:
        return LinearGradient(
          colors: [AppColors.rarityCommon, AppColors.rarityCommon.withOpacity(0.7)],
        );
    }
  }
}
