/// 应用尺寸常量定义
/// 使用8的倍数确保设计一致性
class AppSizes {
  // 间距常量
  static const double spacing4 = 4.0;
  static const double spacing6 = 6.0;
  static const double spacing8 = 8.0;
  static const double spacing10 = 10.0;
  static const double spacing12 = 12.0;
  static const double spacing16 = 16.0;
  static const double spacing20 = 20.0;
  static const double spacing24 = 24.0;
  static const double spacing32 = 32.0;
  static const double spacing48 = 48.0;
  static const double spacing64 = 64.0;
  
  // 圆角常量
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;
  static const double radiusXLarge = 24.0;
  
  // 按钮尺寸
  static const double buttonHeight = 48.0;
  static const double buttonRadius = 12.0;
  
  // 卡片样式
  static const double cardRadius = 16.0;
  static const double cardElevation = 4.0;
  static const double cardPadding = 16.0;
  
  // 输入框样式
  static const double inputRadius = 8.0;
  static const double inputHeight = 48.0;
  
  // 图标尺寸
  static const double iconSmall = 16.0;
  static const double iconMedium = 24.0;
  static const double iconLarge = 32.0;
  static const double iconXLarge = 48.0;
  
  // 私有构造函数，防止实例化
  AppSizes._();
}
