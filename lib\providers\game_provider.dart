import 'package:flutter/foundation.dart';

/// 游戏状态管理Provider
/// 管理游戏的全局状态和设置
class GameProvider extends ChangeNotifier {
  // 私有字段
  bool _isInitialized = false;
  bool _isLoading = false;
  String? _errorMessage;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  double _musicVolume = 0.8;
  double _effectVolume = 0.8;
  
  // 公共getter
  /// 是否已初始化
  bool get isInitialized => _isInitialized;
  
  /// 是否正在加载
  bool get isLoading => _isLoading;
  
  /// 错误信息
  String? get errorMessage => _errorMessage;
  
  /// 是否启用声音
  bool get soundEnabled => _soundEnabled;
  
  /// 是否启用震动
  bool get vibrationEnabled => _vibrationEnabled;
  
  /// 音乐音量
  double get musicVolume => _musicVolume;
  
  /// 音效音量
  double get effectVolume => _effectVolume;
  
  /// 初始化游戏
  Future<void> initialize() async {
    try {
      _setLoading(true);
      _clearError();
      
      // 模拟初始化过程
      await Future.delayed(const Duration(seconds: 1));
      
      // 加载游戏设置
      await _loadGameSettings();
      
      _isInitialized = true;
      debugPrint('游戏初始化完成');
    } catch (e) {
      _setError('游戏初始化失败: $e');
      debugPrint('游戏初始化错误: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// 设置声音开关
  void setSoundEnabled(bool enabled) {
    if (_soundEnabled != enabled) {
      _soundEnabled = enabled;
      _saveGameSettings();
      notifyListeners();
    }
  }
  
  /// 设置震动开关
  void setVibrationEnabled(bool enabled) {
    if (_vibrationEnabled != enabled) {
      _vibrationEnabled = enabled;
      _saveGameSettings();
      notifyListeners();
    }
  }
  
  /// 设置音乐音量
  void setMusicVolume(double volume) {
    final clampedVolume = volume.clamp(0.0, 1.0);
    if (_musicVolume != clampedVolume) {
      _musicVolume = clampedVolume;
      _saveGameSettings();
      notifyListeners();
    }
  }
  
  /// 设置音效音量
  void setEffectVolume(double volume) {
    final clampedVolume = volume.clamp(0.0, 1.0);
    if (_effectVolume != clampedVolume) {
      _effectVolume = clampedVolume;
      _saveGameSettings();
      notifyListeners();
    }
  }
  
  /// 重置游戏设置
  void resetSettings() {
    _soundEnabled = true;
    _vibrationEnabled = true;
    _musicVolume = 0.8;
    _effectVolume = 0.8;
    _saveGameSettings();
    notifyListeners();
  }
  
  /// 清理资源
  void clear() {
    _isInitialized = false;
    _isLoading = false;
    _errorMessage = null;
    notifyListeners();
  }
  
  // 私有方法
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }
  
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }
  
  void _clearError() {
    if (_errorMessage != null) {
      _errorMessage = null;
      notifyListeners();
    }
  }
  
  /// 加载游戏设置
  Future<void> _loadGameSettings() async {
    try {
      // TODO: 从SharedPreferences加载设置
      // 暂时使用默认值
      debugPrint('加载游戏设置');
    } catch (e) {
      debugPrint('加载游戏设置失败: $e');
    }
  }
  
  /// 保存游戏设置
  Future<void> _saveGameSettings() async {
    try {
      // TODO: 保存设置到SharedPreferences
      debugPrint('保存游戏设置');
    } catch (e) {
      debugPrint('保存游戏设置失败: $e');
    }
  }
  
  @override
  void dispose() {
    clear();
    super.dispose();
  }
}
