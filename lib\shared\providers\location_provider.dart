import 'dart:math' show sin, cos, atan2;
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';

/// 位置服务提供者
/// 管理用户位置获取和权限处理
class LocationProvider extends ChangeNotifier {
  Position? _currentPosition;
  bool _isLoading = false;
  String? _errorMessage;
  bool _hasError = false;

  /// 当前位置
  Position? get currentPosition => _currentPosition;

  /// 是否正在加载
  bool get isLoading => _isLoading;

  /// 错误信息
  String? get errorMessage => _errorMessage;

  /// 是否有错误
  bool get hasError => _hasError;

  /// 获取当前位置
  Future<void> getCurrentLocation() async {
    try {
      _setLoading(true);
      _clearError();

      // 检查位置服务是否启用
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw Exception('位置服务未启用，请在设置中开启位置服务');
      }

      // 检查位置权限
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('位置权限被拒绝');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('位置权限被永久拒绝，请在设置中手动开启');
      }

      // 获取当前位置
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      _currentPosition = position;
      debugPrint('位置获取成功: ${position.latitude}, ${position.longitude}');
    } catch (e) {
      _setError(e.toString());
      debugPrint('位置获取失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 获取两点之间的距离（米）
  double getDistanceBetween(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  /// 获取到指定位置的距离
  double? getDistanceToLocation(double latitude, double longitude) {
    if (_currentPosition == null) return null;
    
    return getDistanceBetween(
      _currentPosition!.latitude,
      _currentPosition!.longitude,
      latitude,
      longitude,
    );
  }

  /// 格式化距离显示
  String formatDistance(double distanceInMeters) {
    if (distanceInMeters < 1000) {
      return '${distanceInMeters.round()}m';
    } else {
      return '${(distanceInMeters / 1000).toStringAsFixed(1)}km';
    }
  }

  /// 检查是否在指定位置范围内
  bool isWithinRange(
    double targetLatitude,
    double targetLongitude,
    double rangeInMeters,
  ) {
    if (_currentPosition == null) return false;
    
    double distance = getDistanceBetween(
      _currentPosition!.latitude,
      _currentPosition!.longitude,
      targetLatitude,
      targetLongitude,
    );
    
    return distance <= rangeInMeters;
  }

  /// 开始位置监听
  void startLocationUpdates() {
    const LocationSettings locationSettings = LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: 10, // 移动10米才更新
    );

    Geolocator.getPositionStream(locationSettings: locationSettings)
        .listen((Position position) {
      _currentPosition = position;
      notifyListeners();
      debugPrint('位置更新: ${position.latitude}, ${position.longitude}');
    }, onError: (error) {
      _setError(error.toString());
      debugPrint('位置监听错误: $error');
    });
  }

  /// 停止位置监听
  void stopLocationUpdates() {
    // Geolocator会自动管理流的生命周期
    debugPrint('停止位置监听');
  }

  /// 打开位置设置
  Future<void> openLocationSettings() async {
    await Geolocator.openLocationSettings();
  }

  /// 打开应用设置
  Future<void> openAppSettings() async {
    await openAppSettings();
  }

  /// 检查位置权限状态
  Future<LocationPermissionStatus> checkLocationPermission() async {
    LocationPermission permission = await Geolocator.checkPermission();
    
    switch (permission) {
      case LocationPermission.always:
      case LocationPermission.whileInUse:
        return LocationPermissionStatus.granted;
      case LocationPermission.denied:
        return LocationPermissionStatus.denied;
      case LocationPermission.deniedForever:
        return LocationPermissionStatus.deniedForever;
      case LocationPermission.unableToDetermine:
        return LocationPermissionStatus.unknown;
    }
  }

  /// 请求位置权限
  Future<bool> requestLocationPermission() async {
    try {
      LocationPermission permission = await Geolocator.requestPermission();
      return permission == LocationPermission.always ||
             permission == LocationPermission.whileInUse;
    } catch (e) {
      debugPrint('请求位置权限失败: $e');
      return false;
    }
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 设置错误
  void _setError(String error) {
    _hasError = true;
    _errorMessage = error;
    notifyListeners();
  }

  /// 清除错误
  void _clearError() {
    _hasError = false;
    _errorMessage = null;
    notifyListeners();
  }

  /// 重置状态
  void reset() {
    _currentPosition = null;
    _isLoading = false;
    _hasError = false;
    _errorMessage = null;
    notifyListeners();
  }

  @override
  void dispose() {
    stopLocationUpdates();
    super.dispose();
  }
}

/// 位置权限状态枚举
enum LocationPermissionStatus {
  /// 已授权
  granted,
  
  /// 被拒绝
  denied,
  
  /// 被永久拒绝
  deniedForever,
  
  /// 未知状态
  unknown,
}

/// 位置相关的工具类
class LocationUtils {
  /// 将度数转换为弧度
  static double degreesToRadians(double degrees) {
    return degrees * (3.14159265359 / 180.0);
  }

  /// 计算两点之间的方位角
  static double calculateBearing(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    double startLat = degreesToRadians(startLatitude);
    double startLng = degreesToRadians(startLongitude);
    double endLat = degreesToRadians(endLatitude);
    double endLng = degreesToRadians(endLongitude);

    double dLng = endLng - startLng;

    double y = sin(dLng) * cos(endLat);
    double x = cos(startLat) * sin(endLat) - sin(startLat) * cos(endLat) * cos(dLng);

    double bearing = atan2(y, x);
    bearing = (bearing * 180.0 / 3.14159265359 + 360.0) % 360.0;

    return bearing;
  }

  /// 获取方向描述
  static String getDirectionDescription(double bearing) {
    if (bearing >= 337.5 || bearing < 22.5) {
      return '北';
    } else if (bearing >= 22.5 && bearing < 67.5) {
      return '东北';
    } else if (bearing >= 67.5 && bearing < 112.5) {
      return '东';
    } else if (bearing >= 112.5 && bearing < 157.5) {
      return '东南';
    } else if (bearing >= 157.5 && bearing < 202.5) {
      return '南';
    } else if (bearing >= 202.5 && bearing < 247.5) {
      return '西南';
    } else if (bearing >= 247.5 && bearing < 292.5) {
      return '西';
    } else {
      return '西北';
    }
  }

  /// 验证坐标是否有效
  static bool isValidCoordinate(double latitude, double longitude) {
    return latitude >= -90 && latitude <= 90 && longitude >= -180 && longitude <= 180;
  }
}


