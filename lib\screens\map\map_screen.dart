import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/constants/app_colors.dart';
import '../../core/constants/app_strings.dart';
import '../../core/constants/app_sizes.dart';
import '../../core/services/location_service.dart';
import '../../core/utils/permissions.dart';
import '../../providers/quest_provider.dart';
import '../../models/quest.dart';
import '../quest/quest_detail_screen.dart';

/// 地图页面 - 显示探险地图和附近任务
class MapScreen extends StatefulWidget {
  const MapScreen({super.key});

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  final LocationService _locationService = LocationService.instance;

  // 地图相关状态
  bool _isLoading = true;
  bool _hasLocationPermission = false;
  String? _errorMessage;

  // 当前位置信息
  String _currentLocationText = '获取位置中...';
  List<Quest> _nearbyQuests = [];

  @override
  void initState() {
    super.initState();
    _initializeMap();
  }

  @override
  void dispose() {
    _locationService.stopLocationUpdates();
    super.dispose();
  }

  /// 初始化地图
  Future<void> _initializeMap() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // 请求位置权限
      final hasPermission = await PermissionHelper.requestLocationPermission();
      _hasLocationPermission = hasPermission;

      if (hasPermission) {
        // 初始化位置服务
        final locationInitialized = await _locationService.initialize();

        if (locationInitialized) {
          final position = await _locationService.getCurrentLocation();
          if (position != null) {
            setState(() {
              _currentLocationText = LocationService.formatCoordinates(
                position.latitude,
                position.longitude
              );
            });
          }

          // 开始监听位置变化
          _locationService.startLocationUpdates(
            onLocationUpdate: (position) {
              setState(() {
                _currentLocationText = LocationService.formatCoordinates(
                  position.latitude,
                  position.longitude
                );
              });
            },
          );
        } else {
          setState(() {
            _currentLocationText = '位置服务不可用';
          });
        }
      } else {
        setState(() {
          _currentLocationText = '需要位置权限';
        });
      }

      // 加载附近任务
      await _loadNearbyQuests();

    } catch (e) {
      setState(() {
        _errorMessage = '地图初始化失败: $e';
      });
      debugPrint('地图初始化错误: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 加载附近任务
  Future<void> _loadNearbyQuests() async {
    try {
      final questProvider = context.read<QuestProvider>();
      final allQuests = [
        ...questProvider.availableQuests,
        ...questProvider.activeQuests,
      ];

      // 过滤有位置信息的任务
      final questsWithLocation = allQuests.where((quest) => quest.hasTargetLocation).toList();

      setState(() {
        _nearbyQuests = questsWithLocation;
      });

      debugPrint('加载了 ${questsWithLocation.length} 个附近任务');
    } catch (e) {
      debugPrint('加载附近任务失败: $e');
    }
  }





  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.mapTitle),
        backgroundColor: AppColors.mysteryBlue,
        actions: [
          IconButton(
            icon: const Icon(Icons.my_location),
            onPressed: _hasLocationPermission ? _getCurrentLocation : null,
            tooltip: '获取当前位置',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadNearbyQuests,
            tooltip: '刷新任务',
          ),
        ],
      ),
      body: _buildMapContent(),
    );
  }

  /// 构建地图内容
  Widget _buildMapContent() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: AppSizes.spacing16),
            Text(AppStrings.statusLoading),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: AppSizes.iconXLarge,
              color: AppColors.warningRed,
            ),
            const SizedBox(height: AppSizes.spacing16),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSizes.spacing24),
            ElevatedButton(
              onPressed: _initializeMap,
              child: const Text(AppStrings.buttonRetry),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSizes.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 当前位置卡片
          _buildLocationCard(),

          const SizedBox(height: AppSizes.spacing16),

          // 附近任务列表
          _buildNearbyQuestsSection(),
        ],
      ),
    );
  }

  /// 构建位置信息卡片
  Widget _buildLocationCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.cardPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _hasLocationPermission ? Icons.location_on : Icons.location_off,
                  color: _hasLocationPermission ? AppColors.emeraldGreen : AppColors.warningRed,
                ),
                const SizedBox(width: AppSizes.spacing8),
                Text(
                  AppStrings.mapCurrentLocation,
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: AppSizes.spacing8),
            Text(
              _currentLocationText,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            if (!_hasLocationPermission) ...[
              const SizedBox(height: AppSizes.spacing16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _requestLocationPermission,
                  icon: const Icon(Icons.location_on),
                  label: const Text('开启位置权限'),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建附近任务部分
  Widget _buildNearbyQuestsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppStrings.mapNearbyQuests,
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: AppSizes.spacing8),

        if (_nearbyQuests.isEmpty) ...[
          Card(
            child: Padding(
              padding: const EdgeInsets.all(AppSizes.cardPadding),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.explore_off,
                      size: AppSizes.iconXLarge,
                      color: AppColors.borderGray,
                    ),
                    const SizedBox(height: AppSizes.spacing8),
                    Text(
                      '附近暂无任务',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: AppColors.borderGray,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ] else ...[
          ...(_nearbyQuests.map((quest) => _buildQuestCard(quest))),
        ],
      ],
    );
  }

  /// 构建任务卡片
  Widget _buildQuestCard(Quest quest) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppSizes.spacing8),
      child: InkWell(
        onTap: () => _navigateToQuestDetail(quest),
        borderRadius: BorderRadius.circular(AppSizes.cardRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppSizes.cardPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    _getQuestIcon(quest),
                    color: _getQuestColor(quest),
                  ),
                  const SizedBox(width: AppSizes.spacing8),
                  Expanded(
                    child: Text(
                      quest.title,
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSizes.spacing8,
                      vertical: AppSizes.spacing4,
                    ),
                    decoration: BoxDecoration(
                      color: _getQuestColor(quest).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                    ),
                    child: Text(
                      quest.status.displayName,
                      style: TextStyle(
                        color: _getQuestColor(quest),
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppSizes.spacing8),

              Text(
                quest.description,
                style: Theme.of(context).textTheme.bodyMedium,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: AppSizes.spacing8),

              if (quest.targetLocationName != null) ...[
                Row(
                  children: [
                    const Icon(
                      Icons.place,
                      size: AppSizes.iconSmall,
                      color: AppColors.mysteryBlue,
                    ),
                    const SizedBox(width: AppSizes.spacing4),
                    Expanded(
                      child: Text(
                        quest.targetLocationName!,
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppSizes.spacing4),
              ],

              Row(
                children: [
                  const Icon(
                    Icons.star,
                    size: AppSizes.iconSmall,
                    color: AppColors.treasureGold,
                  ),
                  const SizedBox(width: AppSizes.spacing4),
                  Text(
                    '${quest.experienceReward} EXP',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const Spacer(),
                  const Icon(
                    Icons.arrow_forward_ios,
                    size: AppSizes.iconSmall,
                    color: AppColors.borderGray,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 获取任务图标
  IconData _getQuestIcon(Quest quest) {
    switch (quest.type) {
      case QuestType.exploration:
        return Icons.explore;
      case QuestType.photo:
        return Icons.camera_alt;
      case QuestType.collection:
        return Icons.collections;
      case QuestType.social:
        return Icons.people;
      case QuestType.daily:
        return Icons.today;
    }
  }

  /// 获取任务颜色
  Color _getQuestColor(Quest quest) {
    switch (quest.status) {
      case QuestStatus.available:
        return AppColors.emeraldGreen;
      case QuestStatus.inProgress:
        return AppColors.treasureGold;
      case QuestStatus.completed:
        return AppColors.mysteryBlue;
      case QuestStatus.expired:
        return AppColors.warningRed;
    }
  }

  /// 获取当前位置
  Future<void> _getCurrentLocation() async {
    final position = await _locationService.getCurrentLocation();
    if (position != null) {
      setState(() {
        _currentLocationText = LocationService.formatCoordinates(
          position.latitude,
          position.longitude
        );
      });
    }
  }

  /// 请求位置权限
  Future<void> _requestLocationPermission() async {
    final hasPermission = await PermissionHelper.requestLocationPermission();
    setState(() {
      _hasLocationPermission = hasPermission;
    });

    if (hasPermission) {
      await _getCurrentLocation();
    }
  }

  /// 导航到任务详情
  void _navigateToQuestDetail(Quest quest) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => QuestDetailScreen(quest: quest),
      ),
    );
  }
}
