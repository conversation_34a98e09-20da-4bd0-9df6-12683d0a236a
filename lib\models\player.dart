/// 玩家数据模型
/// 存储玩家的基本信息、等级、经验值等数据
class Player {
  /// 玩家ID
  final String id;
  
  /// 玩家昵称
  final String nickname;
  
  /// 当前等级
  final int level;
  
  /// 当前经验值
  final int experience;
  
  /// 升级所需经验值
  final int experienceToNextLevel;
  
  /// 完成的成就数量
  final int completedAchievements;
  
  /// 总成就数量
  final int totalAchievements;
  
  /// 玩家头像URL
  final String? avatarUrl;
  
  /// 注册时间
  final DateTime createdAt;
  
  /// 最后登录时间
  final DateTime lastLoginAt;
  
  /// 构造函数
  const Player({
    required this.id,
    required this.nickname,
    required this.level,
    required this.experience,
    required this.experienceToNextLevel,
    required this.completedAchievements,
    required this.totalAchievements,
    this.avatarUrl,
    required this.createdAt,
    required this.lastLoginAt,
  });
  
  /// 从JSON创建Player对象
  factory Player.fromJson(Map<String, dynamic> json) {
    return Player(
      id: json['id'] as String,
      nickname: json['nickname'] as String,
      level: json['level'] as int,
      experience: json['experience'] as int,
      experienceToNextLevel: json['experienceToNextLevel'] as int,
      completedAchievements: json['completedAchievements'] as int,
      totalAchievements: json['totalAchievements'] as int,
      avatarUrl: json['avatarUrl'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastLoginAt: DateTime.parse(json['lastLoginAt'] as String),
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nickname': nickname,
      'level': level,
      'experience': experience,
      'experienceToNextLevel': experienceToNextLevel,
      'completedAchievements': completedAchievements,
      'totalAchievements': totalAchievements,
      'avatarUrl': avatarUrl,
      'createdAt': createdAt.toIso8601String(),
      'lastLoginAt': lastLoginAt.toIso8601String(),
    };
  }
  
  /// 复制并修改部分属性
  Player copyWith({
    String? id,
    String? nickname,
    int? level,
    int? experience,
    int? experienceToNextLevel,
    int? completedAchievements,
    int? totalAchievements,
    String? avatarUrl,
    DateTime? createdAt,
    DateTime? lastLoginAt,
  }) {
    return Player(
      id: id ?? this.id,
      nickname: nickname ?? this.nickname,
      level: level ?? this.level,
      experience: experience ?? this.experience,
      experienceToNextLevel: experienceToNextLevel ?? this.experienceToNextLevel,
      completedAchievements: completedAchievements ?? this.completedAchievements,
      totalAchievements: totalAchievements ?? this.totalAchievements,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
    );
  }
  
  /// 计算经验值进度百分比
  double get experienceProgress {
    if (experienceToNextLevel <= 0) return 1.0;
    return experience / experienceToNextLevel;
  }
  
  /// 计算成就完成百分比
  double get achievementProgress {
    if (totalAchievements <= 0) return 0.0;
    return completedAchievements / totalAchievements;
  }
  
  /// 创建默认玩家对象（用于测试）
  factory Player.defaultPlayer() {
    final now = DateTime.now();
    return Player(
      id: 'default_player',
      nickname: '新手探险家',
      level: 1,
      experience: 0,
      experienceToNextLevel: 100,
      completedAchievements: 0,
      totalAchievements: 50,
      createdAt: now,
      lastLoginAt: now,
    );
  }
  
  @override
  String toString() {
    return 'Player(id: $id, nickname: $nickname, level: $level, experience: $experience)';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Player && other.id == id;
  }
  
  @override
  int get hashCode => id.hashCode;
}
