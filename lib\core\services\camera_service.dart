import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import '../utils/permissions.dart';

/// 相机服务类
/// 处理拍照和图片选择功能
class CameraService {
  static CameraService? _instance;
  static CameraService get instance => _instance ??= CameraService._();
  
  CameraService._();
  
  final ImagePicker _picker = ImagePicker();
  
  /// 拍照
  Future<File?> takePicture() async {
    try {
      // 检查相机权限
      final hasPermission = await PermissionHelper.requestCameraPermission();
      if (!hasPermission) {
        debugPrint('没有相机权限');
        return null;
      }
      
      // 拍照
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );
      
      if (image != null) {
        debugPrint('拍照成功: ${image.path}');
        return File(image.path);
      } else {
        debugPrint('用户取消拍照');
        return null;
      }
    } catch (e) {
      debugPrint('拍照失败: $e');
      return null;
    }
  }
  
  /// 从相册选择图片
  Future<File?> pickImageFromGallery() async {
    try {
      // 从相册选择图片
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );
      
      if (image != null) {
        debugPrint('选择图片成功: ${image.path}');
        return File(image.path);
      } else {
        debugPrint('用户取消选择图片');
        return null;
      }
    } catch (e) {
      debugPrint('选择图片失败: $e');
      return null;
    }
  }
  
  /// 显示图片选择选项
  Future<File?> showImagePickerOptions() async {
    // 这个方法需要在UI层调用，这里只是提供接口
    // 实际的选择对话框应该在调用的地方实现
    return null;
  }
  
  /// 检查相机是否可用
  Future<bool> isCameraAvailable() async {
    try {
      final hasPermission = await PermissionHelper.isCameraPermissionGranted();
      return hasPermission;
    } catch (e) {
      debugPrint('检查相机可用性失败: $e');
      return false;
    }
  }
  
  /// 获取图片文件大小（字节）
  static int getImageFileSize(File imageFile) {
    try {
      return imageFile.lengthSync();
    } catch (e) {
      debugPrint('获取图片文件大小失败: $e');
      return 0;
    }
  }
  
  /// 格式化文件大小显示
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }
  
  /// 验证图片文件
  static bool isValidImageFile(File imageFile) {
    try {
      if (!imageFile.existsSync()) {
        return false;
      }
      
      final extension = imageFile.path.toLowerCase();
      return extension.endsWith('.jpg') || 
             extension.endsWith('.jpeg') || 
             extension.endsWith('.png') || 
             extension.endsWith('.gif');
    } catch (e) {
      debugPrint('验证图片文件失败: $e');
      return false;
    }
  }
}
