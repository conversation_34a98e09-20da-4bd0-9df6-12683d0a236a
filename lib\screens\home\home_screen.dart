import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/constants/app_colors.dart';
import '../../core/constants/app_strings.dart';
import '../../core/constants/app_sizes.dart';
import '../../providers/player_provider.dart';
import '../../providers/quest_provider.dart';
import '../../providers/game_provider.dart';
import '../../providers/achievement_provider.dart';
import '../../widgets/common/bottom_navigation.dart';
import '../../widgets/game/game_card.dart';
import '../../widgets/game/game_button.dart';
import '../../widgets/game/game_progress_bar.dart';
import '../map/map_screen.dart';
import '../achievements/achievements_screen.dart';
import '../profile/profile_screen.dart';

/// 首页 - 显示玩家信息和每日任务
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;
  late PageController _pageController;

  // 页面列表
  late List<Widget> _pages;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _pages = [
      _buildHomeContent(),
      const MapScreen(),
      const AchievementsScreen(),
      const ProfileScreen(),
    ];
    // 延迟初始化数据，避免在build过程中调用setState
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeData();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// 初始化数据
  Future<void> _initializeData() async {
    final gameProvider = context.read<GameProvider>();
    final playerProvider = context.read<PlayerProvider>();
    final questProvider = context.read<QuestProvider>();
    final achievementProvider = context.read<AchievementProvider>();

    // 初始化游戏
    if (!gameProvider.isInitialized) {
      await gameProvider.initialize();
    }

    // 初始化玩家数据
    if (!playerProvider.isLoggedIn) {
      await playerProvider.initializePlayer();
    }

    // 初始化任务数据
    await questProvider.initializeQuests();

    // 初始化成就数据
    await achievementProvider.initializeAchievements();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: _currentIndex == 0 ? AppBar(
        title: Text(
          AppStrings.appName,
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            foreground: Paint()
              ..shader = AppColors.treasureGradient.createShader(
                const Rect.fromLTWH(0.0, 0.0, 200.0, 70.0),
              ),
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          Container(
            margin: const EdgeInsets.only(right: AppSizes.spacing8),
            decoration: BoxDecoration(
              color: AppColors.nebula.withOpacity(0.8),
              borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
              border: Border.all(
                color: AppColors.treasureGold.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: IconButton(
              icon: const Icon(Icons.notifications_outlined),
              onPressed: () {
                // TODO: 显示通知
              },
              color: AppColors.treasureGold,
            ),
          ),
        ],
      ) : null,
      body: PageView(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        children: _pages,
      ),
      bottomNavigationBar: CustomBottomNavigation(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
          _pageController.animateToPage(
            index,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        },
      ),
    );
  }

  /// 构建首页内容
  Widget _buildHomeContent() {
    return Consumer4<GameProvider, PlayerProvider, QuestProvider, AchievementProvider>(
      builder: (context, gameProvider, playerProvider, questProvider, achievementProvider, child) {
        // 显示加载状态
        if (gameProvider.isLoading ||
            playerProvider.isLoading ||
            questProvider.isLoading ||
            achievementProvider.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        // 显示错误状态
        if (gameProvider.errorMessage != null ||
            playerProvider.errorMessage != null ||
            questProvider.errorMessage != null ||
            achievementProvider.errorMessage != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: AppSizes.iconXLarge,
                  color: AppColors.warningRed,
                ),
                const SizedBox(height: AppSizes.spacing16),
                Text(
                  gameProvider.errorMessage ??
                  playerProvider.errorMessage ??
                  questProvider.errorMessage ??
                  achievementProvider.errorMessage ??
                  AppStrings.errorGeneral,
                  style: Theme.of(context).textTheme.bodyLarge,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: AppSizes.spacing24),
                ElevatedButton(
                  onPressed: _initializeData,
                  child: const Text(AppStrings.buttonRetry),
                ),
              ],
            ),
          );
        }

        return _buildHomeContentBody(playerProvider, questProvider);
      },
    );
  }

  /// 构建首页内容主体
  Widget _buildHomeContentBody(
    PlayerProvider playerProvider,
    QuestProvider questProvider,
  ) {
    final player = playerProvider.currentPlayer;
    if (player == null) {
      return const Center(
        child: Text(AppStrings.statusDeveloping),
      );
    }

    return Container(
      decoration: BoxDecoration(
        gradient: AppColors.backgroundGradient,
      ),
      child: SingleChildScrollView(
        padding: EdgeInsets.only(
          left: AppSizes.spacing16,
          right: AppSizes.spacing16,
          top: AppSizes.spacing16 + MediaQuery.of(context).padding.top + 60,
          bottom: AppSizes.spacing16,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 玩家状态卡片
            _buildPlayerStatsCard(player),

            const SizedBox(height: AppSizes.spacing24),

            // 每日任务
            _buildDailyQuestsSection(questProvider),

            const SizedBox(height: AppSizes.spacing24),

            // 快速操作
            _buildQuickActionsSection(),

            const SizedBox(height: AppSizes.spacing24),

            // 最近成就
            _buildRecentAchievements(),
          ],
        ),
      ),
    );
  }

  /// 构建玩家状态卡片
  Widget _buildPlayerStatsCard(dynamic player) {
    return GameCard(
      style: GameCardStyle.treasure,
      glowEffect: true,
      child: Column(
        children: [
          // 玩家头像和基本信息
          Row(
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: AppColors.treasureGradient,
                  border: Border.all(
                    color: AppColors.treasureGold,
                    width: 3,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.treasureGold.withOpacity(0.5),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Center(
                  child: Text(
                    player.nickname.isNotEmpty ? player.nickname[0] : '?',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: AppColors.deepSpace,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: AppSizes.spacing16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      player.nickname,
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        color: AppColors.treasureGold,
                      ),
                    ),
                    const SizedBox(height: AppSizes.spacing4),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSizes.spacing12,
                        vertical: AppSizes.spacing4,
                      ),
                      decoration: BoxDecoration(
                        gradient: AppColors.magicGradient,
                        borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
                      ),
                      child: Text(
                        '等级 ${player.level}',
                        style: TextStyle(
                          color: AppColors.starWhite,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // 等级徽章
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: AppColors.magicGradient,
                  border: Border.all(
                    color: AppColors.neonCyan,
                    width: 2,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '${player.level}',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppColors.starWhite,
                      ),
                    ),
                    Text(
                      'LV',
                      style: TextStyle(
                        fontSize: 10,
                        color: AppColors.starSilver,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: AppSizes.spacing24),

          // 经验值进度条
          GameProgressBar(
            value: player.experienceProgress,
            label: '经验值',
            currentText: '${player.experience}',
            maxText: '${player.experienceToNextLevel}',
            style: GameProgressStyle.experience,
            height: 28,
          ),

          const SizedBox(height: AppSizes.spacing16),

          // 统计信息
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  '完成任务',
                  '3', // TODO: 从questProvider获取
                  Icons.assignment_turned_in,
                  AppColors.emeraldGreen,
                ),
              ),
              Container(
                width: 1,
                height: 40,
                color: AppColors.starGray.withOpacity(0.3),
              ),
              Expanded(
                child: _buildStatItem(
                  '获得成就',
                  '${player.completedAchievements}',
                  Icons.emoji_events,
                  AppColors.treasureGold,
                ),
              ),
              Container(
                width: 1,
                height: 40,
                color: AppColors.starGray.withOpacity(0.3),
              ),
              Expanded(
                child: _buildStatItem(
                  '探索天数',
                  '${DateTime.now().difference(player.createdAt).inDays + 1}',
                  Icons.explore,
                  AppColors.adventureOrange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建每日任务部分
  Widget _buildDailyQuestsSection(QuestProvider questProvider) {
    final dailyQuests = questProvider.dailyQuests;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.today,
              color: AppColors.adventureOrange,
              size: 28,
            ),
            const SizedBox(width: AppSizes.spacing8),
            Text(
              '每日挑战',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: AppColors.adventureOrange,
              ),
            ),
            const Spacer(),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppSizes.spacing12,
                vertical: AppSizes.spacing4,
              ),
              decoration: BoxDecoration(
                gradient: AppColors.adventureGradient,
                borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
              ),
              child: Text(
                '${dailyQuests.where((q) => q.isCompleted).length}/${dailyQuests.length}',
                style: TextStyle(
                  color: AppColors.starWhite,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSizes.spacing16),

        if (dailyQuests.isEmpty)
          GameCard(
            style: GameCardStyle.glass,
            child: Center(
              child: Column(
                children: [
                  Icon(
                    Icons.check_circle_outline,
                    size: 48,
                    color: AppColors.starGray,
                  ),
                  const SizedBox(height: AppSizes.spacing8),
                  Text(
                    '今日任务已全部完成！',
                    style: TextStyle(
                      color: AppColors.starSilver,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          )
        else
          ...dailyQuests.map((quest) => GameCard(
            style: quest.isCompleted ? GameCardStyle.success : GameCardStyle.adventure,
            margin: const EdgeInsets.only(bottom: AppSizes.spacing12),
            onTap: quest.isCompleted ? null : () {
              // TODO: 导航到任务详情
            },
            child: Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: quest.isCompleted
                        ? AppColors.successGradient
                        : AppColors.adventureGradient,
                    border: Border.all(
                      color: quest.isCompleted
                          ? AppColors.emeraldGreen
                          : AppColors.adventureOrange,
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    quest.isCompleted ? Icons.check : Icons.assignment,
                    color: AppColors.starWhite,
                    size: 28,
                  ),
                ),
                const SizedBox(width: AppSizes.spacing16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        quest.title,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: AppColors.starWhite,
                        ),
                      ),
                      const SizedBox(height: AppSizes.spacing4),
                      Text(
                        quest.description,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.starSilver,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSizes.spacing8,
                        vertical: AppSizes.spacing4,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.treasureGold.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                      ),
                      child: Text(
                        '+${quest.experienceReward} EXP',
                        style: TextStyle(
                          color: AppColors.treasureGold,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    if (!quest.isCompleted) ...[
                      const SizedBox(height: AppSizes.spacing8),
                      Icon(
                        Icons.arrow_forward_ios,
                        color: AppColors.starGray,
                        size: 16,
                      ),
                    ],
                  ],
                ),
              ],
            ),
          )),
      ],
    );
  }

  /// 构建统计项目
  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(
          icon,
          size: 24,
          color: color,
        ),
        const SizedBox(height: AppSizes.spacing8),
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: AppColors.starSilver,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// 构建快速操作部分
  Widget _buildQuickActionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.flash_on,
              color: AppColors.neonCyan,
              size: 28,
            ),
            const SizedBox(width: AppSizes.spacing8),
            Text(
              '快速行动',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: AppColors.neonCyan,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSizes.spacing16),

        Row(
          children: [
            Expanded(
              child: GameButton(
                text: '探索地图',
                icon: Icons.explore,
                style: GameButtonStyle.adventure,
                onPressed: () {
                  // TODO: 导航到地图
                },
              ),
            ),
            const SizedBox(width: AppSizes.spacing12),
            Expanded(
              child: GameButton(
                text: '查看成就',
                icon: Icons.emoji_events,
                style: GameButtonStyle.treasure,
                onPressed: () {
                  // TODO: 导航到成就
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSizes.spacing12),

        Row(
          children: [
            Expanded(
              child: GameButton(
                text: '拍照任务',
                icon: Icons.camera_alt,
                style: GameButtonStyle.primary,
                onPressed: () {
                  // TODO: 导航到相机
                },
              ),
            ),
            const SizedBox(width: AppSizes.spacing12),
            Expanded(
              child: GameButton(
                text: '个人资料',
                icon: Icons.person,
                style: GameButtonStyle.outline,
                onPressed: () {
                  // TODO: 导航到个人资料
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建最近成就部分
  Widget _buildRecentAchievements() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.emoji_events,
              color: AppColors.treasureGold,
              size: 28,
            ),
            const SizedBox(width: AppSizes.spacing8),
            Text(
              '最新成就',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: AppColors.treasureGold,
              ),
            ),
            const Spacer(),
            GameButton(
              text: '查看全部',
              style: GameButtonStyle.outline,
              width: 100,
              height: 36,
              onPressed: () {
                // TODO: 导航到成就页面
              },
            ),
          ],
        ),
        const SizedBox(height: AppSizes.spacing16),

        GameCard(
          style: GameCardStyle.treasure,
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: AppColors.treasureGradient,
                  border: Border.all(
                    color: AppColors.treasureGold,
                    width: 2,
                  ),
                ),
                child: Icon(
                  Icons.explore,
                  color: AppColors.deepSpace,
                  size: 28,
                ),
              ),
              const SizedBox(width: AppSizes.spacing16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '初次探险',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppColors.treasureGold,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppSizes.spacing4),
                    Text(
                      '完成第一个探索任务',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.starSilver,
                      ),
                    ),
                    const SizedBox(height: AppSizes.spacing8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSizes.spacing8,
                        vertical: AppSizes.spacing4,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.emeraldGreen.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                      ),
                      child: Text(
                        '已完成 +50 EXP',
                        style: TextStyle(
                          color: AppColors.emeraldGreen,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.check_circle,
                color: AppColors.emeraldGreen,
                size: 32,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
