import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/constants/app_colors.dart';
import '../../core/constants/app_strings.dart';
import '../../core/constants/app_sizes.dart';
import '../../providers/player_provider.dart';
import '../../providers/quest_provider.dart';
import '../../providers/game_provider.dart';
import '../../widgets/common/bottom_navigation.dart';

/// 首页 - 显示玩家信息和每日任务
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  /// 初始化数据
  Future<void> _initializeData() async {
    final gameProvider = context.read<GameProvider>();
    final playerProvider = context.read<PlayerProvider>();
    final questProvider = context.read<QuestProvider>();

    // 初始化游戏
    if (!gameProvider.isInitialized) {
      await gameProvider.initialize();
    }

    // 初始化玩家数据
    if (!playerProvider.isLoggedIn) {
      await playerProvider.initializePlayer();
    }

    // 初始化任务数据
    await questProvider.initializeQuests();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.appName),
        backgroundColor: AppColors.mysteryBlue,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // TODO: 显示通知
            },
          ),
        ],
      ),
      body: Consumer3<GameProvider, PlayerProvider, QuestProvider>(
        builder: (context, gameProvider, playerProvider, questProvider, child) {
          // 显示加载状态
          if (gameProvider.isLoading || 
              playerProvider.isLoading || 
              questProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          // 显示错误状态
          if (gameProvider.errorMessage != null ||
              playerProvider.errorMessage != null ||
              questProvider.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: AppSizes.iconXLarge,
                    color: AppColors.warningRed,
                  ),
                  const SizedBox(height: AppSizes.spacing16),
                  Text(
                    gameProvider.errorMessage ??
                    playerProvider.errorMessage ??
                    questProvider.errorMessage ??
                    AppStrings.errorGeneral,
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: AppSizes.spacing24),
                  ElevatedButton(
                    onPressed: _initializeData,
                    child: const Text(AppStrings.buttonRetry),
                  ),
                ],
              ),
            );
          }

          return _buildHomeContent(context, playerProvider, questProvider);
        },
      ),
      bottomNavigationBar: CustomBottomNavigation(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
      ),
    );
  }

  /// 构建首页内容
  Widget _buildHomeContent(
    BuildContext context,
    PlayerProvider playerProvider,
    QuestProvider questProvider,
  ) {
    final player = playerProvider.currentPlayer;
    if (player == null) {
      return const Center(
        child: Text(AppStrings.statusDeveloping),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSizes.spacing16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 欢迎信息
          _buildWelcomeCard(context, player),
          
          const SizedBox(height: AppSizes.spacing16),
          
          // 玩家状态卡片
          _buildPlayerStatsCard(context, player),
          
          const SizedBox(height: AppSizes.spacing16),
          
          // 每日任务
          _buildDailyQuestsSection(context, questProvider),
          
          const SizedBox(height: AppSizes.spacing16),
          
          // 快速操作
          _buildQuickActionsSection(context),
        ],
      ),
    );
  }

  /// 构建欢迎卡片
  Widget _buildWelcomeCard(BuildContext context, dynamic player) {
    return Card(
      child: Container(
        padding: const EdgeInsets.all(AppSizes.cardPadding),
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: AppColors.treasureGold,
              child: Text(
                player.nickname.isNotEmpty ? player.nickname[0] : '?',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(width: AppSizes.spacing16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${AppStrings.homeWelcome}',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: AppSizes.spacing4),
                  Text(
                    player.nickname,
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建玩家状态卡片
  Widget _buildPlayerStatsCard(BuildContext context, dynamic player) {
    return Card(
      child: Container(
        padding: const EdgeInsets.all(AppSizes.cardPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '玩家状态',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: AppSizes.spacing16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    AppStrings.homeCurrentLevel,
                    '${player.level}',
                    Icons.star,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    '成就',
                    '${player.completedAchievements}/${player.totalAchievements}',
                    Icons.emoji_events,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSizes.spacing16),
            Text(
              AppStrings.homeExperience,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: AppSizes.spacing8),
            LinearProgressIndicator(
              value: player.experienceProgress,
              backgroundColor: AppColors.borderGray,
              valueColor: const AlwaysStoppedAnimation<Color>(AppColors.treasureGold),
            ),
            const SizedBox(height: AppSizes.spacing4),
            Text(
              '${player.experience}/${player.experienceToNextLevel}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建统计项目
  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Column(
      children: [
        Icon(
          icon,
          size: AppSizes.iconLarge,
          color: AppColors.mysteryBlue,
        ),
        const SizedBox(height: AppSizes.spacing8),
        Text(
          value,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            color: AppColors.mysteryBlue,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  /// 构建每日任务部分
  Widget _buildDailyQuestsSection(BuildContext context, QuestProvider questProvider) {
    final dailyQuests = questProvider.dailyQuests;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppStrings.homeDailyQuest,
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        const SizedBox(height: AppSizes.spacing8),
        if (dailyQuests.isEmpty)
          const Card(
            child: Padding(
              padding: EdgeInsets.all(AppSizes.cardPadding),
              child: Text('暂无每日任务'),
            ),
          )
        else
          ...dailyQuests.map((quest) => Card(
            child: ListTile(
              leading: const Icon(Icons.assignment),
              title: Text(quest.title),
              subtitle: Text(quest.description),
              trailing: quest.isCompleted
                  ? const Icon(Icons.check_circle, color: AppColors.emeraldGreen)
                  : const Icon(Icons.arrow_forward_ios),
              onTap: () {
                // TODO: 导航到任务详情
              },
            ),
          )),
      ],
    );
  }

  /// 构建快速操作部分
  Widget _buildQuickActionsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '快速操作',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        const SizedBox(height: AppSizes.spacing8),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  // TODO: 导航到地图
                },
                icon: const Icon(Icons.map),
                label: const Text('探索地图'),
              ),
            ),
            const SizedBox(width: AppSizes.spacing8),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  // TODO: 导航到成就
                },
                icon: const Icon(Icons.emoji_events),
                label: const Text('查看成就'),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
