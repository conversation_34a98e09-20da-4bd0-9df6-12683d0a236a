# 🎮 Cursor AI 开发提示词 - 宝藏奇缘：现实秘境

## 📋 项目基本信息
- **项目名称**：treasure_odyssey
- **游戏中文名**：宝藏奇缘：现实秘境  
- **游戏英文名**：Treasure Odyssey: Realms of Mystery
- **开发框架**：Flutter
- **目标平台**：Android + iOS
- **游戏类型**：现实探险 + 区块链经济手游

## 🎨 UI设计严格要求

### 🌈 色彩规范
请严格遵循以下配色方案：
```dart
// 主色调
static const Color primaryGold = Color(0xFFFFD700);
static const Color primaryOrange = Color(0xFFFF8C00);
static const Color mysteryPurple = Color(0xFF8A2BE2);
static const Color deepPurple = Color(0xFF9370DB);

// 辅助色
static const Color skyBlue = Color(0xFF87CEEB);
static const Color limeGreen = Color(0xFF32CD32);
static const Color softPink = Color(0xFFFFB6C1);
static const Color cardWhite = Color(0xFFFFFFF8);

// 背景渐变
static const LinearGradient backgroundGradient = LinearGradient(
  begin: Alignment.topCenter,
  end: Alignment.bottomCenter,
  colors: [Color(0xFFFFE4B5), Color(0xFFE6E6FA)],
);
```

### 🎯 界面设计原则
1. **卡片式设计**：所有内容区域使用圆角卡片(borderRadius: 16.0)
2. **阴影效果**：卡片使用柔和阴影(elevation: 4.0)
3. **动画效果**：页面切换使用Hero动画，按钮点击有缩放效果
4. **图标风格**：使用Material Icons，配合自定义游戏图标
5. **字体规范**：中文使用'PingFang SC'，英文使用'Comic Sans MS'

### 📱 布局要求
- **安全区域**：所有内容必须在SafeArea内
- **单手操作**：主要功能按钮在屏幕下半部分
- **响应式设计**：适配不同屏幕尺寸
- **底部导航**：固定5个主要功能入口

## 🏗️ 代码架构要求

### 📁 目录结构
```
lib/
├── main.dart
├── app/
│   ├── app.dart
│   └── routes/
├── core/
│   ├── constants/
│   ├── theme/
│   ├── utils/
│   └── services/
├── features/
│   ├── authentication/
│   ├── map/
│   ├── inventory/
│   ├── achievements/
│   ├── marketplace/
│   └── profile/
├── shared/
│   ├── widgets/
│   ├── models/
│   └── providers/
└── assets/
    ├── images/
    ├── icons/
    └── animations/
```

### 🔧 技术栈要求
必须使用以下依赖包：
```yaml
dependencies:
  flutter: sdk: flutter
  # 状态管理
  provider: ^6.0.5
  # 路由管理  
  go_router: ^12.0.0
  # 网络请求
  dio: ^5.3.2
  # 本地存储
  shared_preferences: ^2.2.2
  # 地图服务
  google_maps_flutter: ^2.5.0
  geolocator: ^10.1.0
  # UI组件
  animate_do: ^3.1.2
  lottie: ^2.7.0
  cached_network_image: ^3.3.0
  # 区块链集成
  web3dart: ^2.7.3
  # 其他工具
  image_picker: ^1.0.4
  qr_code_scanner: ^1.0.1
```

## 🎮 功能模块开发要求

### 1. 主界面 (MainScreen)
```dart
// 必须包含的组件
- BottomNavigationBar (5个Tab)
- AppBar with 宝石币余额显示
- 每日任务横幅卡片
- 快捷功能网格 (3x2)
- 活动预告轮播图
- FloatingActionButton (扫码功能)
```

### 2. 探险地图 (MapScreen)  
```dart
// 必须实现的功能
- GoogleMap集成显示用户位置
- 自定义Marker显示任务点
- 底部Sheet显示附近任务列表
- 搜索框功能
- 路径规划按钮
- AR扫描入口
```

### 3. 成就收藏 (AchievementScreen)
```dart
// 卡片展示要求  
- GridView.builder 网格布局
- 卡片翻转动画效果
- 稀有度光效区分
- 筛选和排序功能
- 详情弹窗展示
- 分享功能
```

### 4. 交易市场 (MarketplaceScreen)
```dart
// 交易功能要求
- TabBar切换: 购买/出售/我的订单
- 商品列表with价格趋势图
- 搜索和筛选功能  
- 购买/出售弹窗
- 钱包余额显示
- 交易历史记录
```

### 5. 个人中心 (ProfileScreen)
```dart
// 个人信息展示
- 头像 + 昵称 + 等级
- 成就统计卡片
- 钱包管理入口
- 设置功能列表
- 排行榜入口
- 邀请好友功能
```

## 🎯 游戏逻辑要求

### 任务系统
```dart
class Mission {
  final String id;
  final String title;
  final String description;
  final MissionType type; // 签到/消费/拍照/社交
  final Location? targetLocation;
  final List<MissionReward> rewards;
  final DateTime startTime;
  final DateTime endTime;
  final MissionStatus status;
}
```

### 成就卡片系统
```dart
class AchievementCard {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final CardRarity rarity; // 普通/稀有/传说/限定
  final List<CardAttribute> attributes;
  final bool isOwned;
  final DateTime? obtainedAt;
  final String? nftTokenId;
}
```

### 区块链集成
```dart
class BlockchainService {
  // 必须实现的方法
  Future<void> connectWallet();
  Future<void> disconnectWallet();  
  Future<double> getTokenBalance(String tokenAddress);
  Future<String> transferToken(String to, double amount);
  Future<void> mintNFT(String metadata);
  Future<List<NFT>> getUserNFTs();
}
```

## 🔒 开发规范要求

### 代码质量
1. **空安全**：所有代码必须支持null safety
2. **异常处理**：网络请求必须有try-catch
3. **加载状态**：所有异步操作显示loading
4. **错误提示**：用户友好的错误信息
5. **代码注释**：关键逻辑必须有中文注释

### 性能优化
1. **图片缓存**：使用CachedNetworkImage
2. **列表优化**：长列表使用ListView.builder
3. **状态管理**：合理使用Provider避免重建
4. **内存管理**：及时dispose控制器和监听器

### 用户体验
1. **响应速度**：页面切换<300ms
2. **网络超时**：所有请求设置10s超时
3. **离线支持**：缓存关键数据支持离线查看
4. **无障碍支持**：添加semanticLabel

## 🎨 美术资源要求

### 图标设计
- **尺寸**：提供1x, 2x, 3x三种尺寸
- **格式**：PNG透明背景
- **风格**：Q版卡通，色彩饱满
- **命名**：使用英文+下划线命名

### 动画效果
- **页面切换**：Hero动画 + 淡入淡出
- **按钮交互**：点击缩放 + 涟漪效果  
- **卡片展示**：翻转 + 光效动画
- **加载动画**：Lottie JSON格式

## 🚀 开发优先级

### 第一阶段 (MVP)
1. 用户注册登录系统
2. 主界面框架搭建
3. 基础地图功能
4. 简单任务系统
5. 成就卡片展示

### 第二阶段 (核心功能)
1. 位置验证系统
2. 消费验证对接
3. 交易市场基础功能
4. 区块链钱包集成
5. 社交功能

### 第三阶段 (增强功能)
1. AR扫描功能
2. 城市大作战玩法
3. 高级交易功能
4. 数据分析统计
5. 运营活动系统

## ⚠️ 重要开发注意事项

1. **严格遵循UI设计**：所有界面必须符合设计规范，不得随意更改
2. **游戏体验优先**：界面交互要符合游戏特点，不是普通App
3. **性能优化**：手机游戏对性能要求高，注意内存和CPU使用
4. **安全考虑**：涉及区块链和支付，必须做好安全防护
5. **用户隐私**：位置等敏感信息的收集和使用要合规

## 📞 开发过程中的沟通
- 遇到设计问题时，优先询问而不是自己决定
- 技术难点及时反馈，共同寻找解决方案
- 定期提交代码，保证开发进度可追踪
- 严格按照优先级顺序开发，不要跳跃