# 宝藏奇缘：现实秘境 (Treasure Odyssey) - 开发规则

## 项目概述
这是一款基于Flutter的现实世界探险手机游戏。玩家通过GPS定位在真实世界中完成任务，收集成就卡片。

## 核心开发原则

### 1. 项目结构 - 严格遵循
```
lib/
├── main.dart                 # 应用入口
├── app.dart                  # App根组件
├── core/
│   ├── constants/
│   │   ├── app_colors.dart   # 颜色常量
│   │   ├── app_strings.dart  # 文本常量
│   │   └── app_sizes.dart    # 尺寸常量
│   ├── theme/
│   │   └── app_theme.dart    # 主题配置
│   ├── utils/
│   │   ├── permissions.dart  # 权限管理
│   │   └── location_helper.dart
│   └── services/
│       ├── database_service.dart
│       └── location_service.dart
├── models/
│   ├── player.dart
│   ├── achievement.dart
│   └── quest.dart
├── providers/
│   ├── game_provider.dart
│   ├── player_provider.dart
│   └── quest_provider.dart
├── screens/
│   ├── home/
│   ├── map/
│   ├── achievements/
│   └── profile/
├── widgets/
│   ├── common/
│   └── custom/
└── data/
    ├── database/
    └── repositories/
```

### 2. 命名规范 - 必须遵循
- 文件名：snake_case (例：home_screen.dart)
- 类名：PascalCase (例：HomeScreen)
- 变量/方法：camelCase (例：currentLevel)
- 常量：UPPER_SNAKE_CASE (例：DEFAULT_LEVEL)
- 私有成员：下划线开头 (例：_privateMethod)

### 3. 颜色系统 - 只能使用以下颜色
```dart
class AppColors {
  static const Color treasureGold = Color(0xFFFFD700);
  static const Color mysteryBlue = Color(0xFF1E3A8A);
  static const Color emeraldGreen = Color(0xFF10B981);
  static const Color backgroundWhite = Color(0xFFFAFAFA);
  static const Color textBlack = Color(0xFF1F2937);
  static const Color borderGray = Color(0xFFE5E7EB);
  static const Color warningRed = Color(0xFFEF4444);
}
```

### 4. 组件样式标准
- 按钮高度：48px，圆角：12px
- 卡片圆角：16px，阴影：elevation 4
- 输入框圆角：8px
- 间距使用：8, 16, 24, 32的倍数

### 5. 代码质量要求
- 所有公共方法必须有中文注释
- 每个Screen类顶部添加功能说明注释
- 异步方法必须包含错误处理
- 使用const构造函数优化性能
- 避免在build方法中创建对象

### 6. 状态管理模式
使用Provider模式，每个Provider必须：
- 继承ChangeNotifier
- 提供clear()方法用于清理
- 在dispose()中正确释放资源
- 使用private字段存储状态

### 7. 必需依赖包
```yaml
dependencies:
  flutter:
    sdk: flutter
  provider: ^6.1.1
  geolocator: ^10.1.0
  google_maps_flutter: ^2.5.0
  camera: ^0.10.5
  shared_preferences: ^2.2.2
  sqflite: ^2.3.0
  cached_network_image: ^3.3.0
  http: ^1.1.0
  permission_handler: ^11.1.0
  image_picker: ^1.0.4
```

### 8. UI组件规范

#### 按钮组件
```dart
ElevatedButton(
  style: ElevatedButton.styleFrom(
    backgroundColor: AppColors.treasureGold,
    foregroundColor: Colors.white,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(12),
    ),
    minimumSize: const Size(double.infinity, 48),
  ),
  onPressed: onPressed,
  child: Text(text),
)
```

#### 卡片组件
```dart
Card(
  elevation: 4,
  shape: RoundedRectangleBorder(
    borderRadius: BorderRadius.circular(16),
  ),
  child: Container(
    padding: const EdgeInsets.all(16),
    child: content,
  ),
)
```

### 9. 错误处理模式
```dart
try {
  // 异步操作
  final result = await someAsyncOperation();
  return result;
} catch (e) {
  debugPrint('错误信息: $e');
  // 显示用户友好的错误提示
  _showErrorSnackBar('操作失败，请重试');
  return null;
}
```

### 10. 权限检查模式
在使用GPS、相机等功能前，必须先检查权限：
```dart
Future<bool> _checkPermission() async {
  final permission = await Permission.location.status;
  if (permission.isGranted) {
    return true;
  } else {
    final result = await Permission.location.request();
    return result.isGranted;
  }
}
```

## 优先开发顺序

### Phase 1: 基础框架
1. 创建项目结构和常量定义
2. 实现基础主题和颜色系统
3. 创建底部导航和主页框架
4. 实现Player模型和基础数据结构

### Phase 2: 核心功能
1. 集成地图功能和GPS定位
2. 实现任务系统基础功能
3. 创建成就卡片展示页面
4. 添加相机拍照功能

### Phase 3: 完善体验
1. 添加动画效果和转场
2. 实现数据持久化
3. 优化性能和用户体验
4. 添加错误处理和加载状态

## 重要约定

### 响应式设计
- 使用MediaQuery.of(context).size获取屏幕尺寸
- 支持不同屏幕密度和尺寸
- 考虑安全区域适配

### 性能优化
- 列表使用ListView.builder
- 图片使用CachedNetworkImage
- 适当使用const构造函数
- 避免不必要的rebuild

### 国际化准备
- 所有用户可见文本定义为常量
- 使用AppStrings类管理文本
- 为后续多语言支持做准备

### 应用测试
- 使用mobile-mcp工具进行应用测试

## 禁止事项
- 不要使用硬编码的颜色值
- 不要在build方法中进行复杂计算
- 不要忽略异步操作的错误处理
- 不要创建过深的widget嵌套
- 不要在没有权限检查的情况下使用系统功能

## 代码示例模板

### Screen模板
```dart
/// 首页 - 显示玩家信息和每日任务
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('宝藏奇缘'),
        backgroundColor: AppColors.mysteryBlue,
      ),
      body: const Center(
        child: Text('开发中...'),
      ),
    );
  }
}
```

请严格按照以上规则开发，确保代码质量和项目一致性！