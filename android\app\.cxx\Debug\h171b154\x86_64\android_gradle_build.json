{"buildFiles": ["D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\project\\treasure_odyssey\\android\\app\\.cxx\\Debug\\h171b154\\x86_64", "clean"]], "buildTargetsCommandComponents": ["D:\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\project\\treasure_odyssey\\android\\app\\.cxx\\Debug\\h171b154\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\AndroidSDK\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\AndroidSDK\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}