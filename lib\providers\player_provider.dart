import 'package:flutter/foundation.dart';
import '../models/player.dart';

/// 玩家数据管理Provider
/// 管理玩家信息、等级、经验值等数据
class PlayerProvider extends ChangeNotifier {
  // 私有字段
  Player? _currentPlayer;
  bool _isLoading = false;
  String? _errorMessage;
  
  // 公共getter
  /// 当前玩家
  Player? get currentPlayer => _currentPlayer;
  
  /// 是否正在加载
  bool get isLoading => _isLoading;
  
  /// 错误信息
  String? get errorMessage => _errorMessage;
  
  /// 是否已登录
  bool get isLoggedIn => _currentPlayer != null;
  
  /// 初始化玩家数据
  Future<void> initializePlayer() async {
    try {
      _setLoading(true);
      _clearError();
      
      // 模拟加载玩家数据
      await Future.delayed(const Duration(milliseconds: 500));
      
      // 创建默认玩家（实际应用中应从数据库或API加载）
      _currentPlayer = Player.defaultPlayer();
      
      debugPrint('玩家数据初始化完成: ${_currentPlayer?.nickname}');
    } catch (e) {
      _setError('加载玩家数据失败: $e');
      debugPrint('玩家数据初始化错误: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// 更新玩家昵称
  Future<void> updateNickname(String nickname) async {
    if (_currentPlayer == null) return;
    
    try {
      _setLoading(true);
      _clearError();
      
      // 模拟网络请求
      await Future.delayed(const Duration(milliseconds: 300));
      
      _currentPlayer = _currentPlayer!.copyWith(nickname: nickname);
      
      // TODO: 保存到数据库
      await _savePlayerData();
      
      debugPrint('玩家昵称更新为: $nickname');
    } catch (e) {
      _setError('更新昵称失败: $e');
      debugPrint('更新昵称错误: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// 增加经验值
  Future<void> addExperience(int experience) async {
    if (_currentPlayer == null || experience <= 0) return;
    
    try {
      final currentExp = _currentPlayer!.experience + experience;
      final expToNext = _currentPlayer!.experienceToNextLevel;
      
      int newLevel = _currentPlayer!.level;
      int remainingExp = currentExp;
      int newExpToNext = expToNext;
      
      // 检查是否升级
      while (remainingExp >= newExpToNext) {
        remainingExp -= newExpToNext;
        newLevel++;
        newExpToNext = _calculateExpToNextLevel(newLevel);
      }
      
      _currentPlayer = _currentPlayer!.copyWith(
        level: newLevel,
        experience: remainingExp,
        experienceToNextLevel: newExpToNext,
      );
      
      // TODO: 保存到数据库
      await _savePlayerData();
      
      debugPrint('玩家获得经验值: $experience, 当前等级: $newLevel');
    } catch (e) {
      _setError('更新经验值失败: $e');
      debugPrint('更新经验值错误: $e');
    }
  }
  
  /// 完成成就
  Future<void> completeAchievement() async {
    if (_currentPlayer == null) return;
    
    try {
      final newCompletedCount = _currentPlayer!.completedAchievements + 1;
      
      _currentPlayer = _currentPlayer!.copyWith(
        completedAchievements: newCompletedCount,
      );
      
      // TODO: 保存到数据库
      await _savePlayerData();
      
      debugPrint('玩家完成成就，总数: $newCompletedCount');
    } catch (e) {
      _setError('更新成就失败: $e');
      debugPrint('更新成就错误: $e');
    }
  }
  
  /// 更新最后登录时间
  Future<void> updateLastLogin() async {
    if (_currentPlayer == null) return;
    
    try {
      _currentPlayer = _currentPlayer!.copyWith(
        lastLoginAt: DateTime.now(),
      );
      
      // TODO: 保存到数据库
      await _savePlayerData();
      
      debugPrint('更新最后登录时间');
    } catch (e) {
      debugPrint('更新登录时间错误: $e');
    }
  }
  
  /// 登出
  void logout() {
    _currentPlayer = null;
    _clearError();
    notifyListeners();
    debugPrint('玩家已登出');
  }
  
  /// 清理数据
  void clear() {
    _currentPlayer = null;
    _isLoading = false;
    _errorMessage = null;
    notifyListeners();
  }
  
  // 私有方法
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }
  
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }
  
  void _clearError() {
    if (_errorMessage != null) {
      _errorMessage = null;
      notifyListeners();
    }
  }
  
  /// 计算升级所需经验值
  int _calculateExpToNextLevel(int level) {
    // 简单的经验值计算公式：基础100 + 等级 * 50
    return 100 + (level - 1) * 50;
  }
  
  /// 保存玩家数据
  Future<void> _savePlayerData() async {
    try {
      // TODO: 实现数据持久化
      debugPrint('保存玩家数据');
    } catch (e) {
      debugPrint('保存玩家数据失败: $e');
    }
  }
  
  @override
  void dispose() {
    clear();
    super.dispose();
  }
}
