import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_strings.dart';
import '../../core/constants/app_sizes.dart';
import '../../shared/providers/location_provider.dart';
import '../../shared/widgets/cute_card.dart';
import '../../shared/widgets/cute_button.dart';

/// 探险地图页面
/// 显示用户位置和附近的任务点
class MapScreen extends StatefulWidget {
  const MapScreen({super.key});

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  GoogleMapController? _mapController;
  final Set<Marker> _markers = {};
  
  @override
  void initState() {
    super.initState();
    _initializeMap();
  }

  /// 初始化地图
  void _initializeMap() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final locationProvider = Provider.of<LocationProvider>(context, listen: false);
      locationProvider.getCurrentLocation();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: AppColors.backgroundGradient,
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          title: const Text('🗺️ 探险地图'),
          backgroundColor: Colors.transparent,
          actions: [
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: _showSearchDialog,
            ),
            IconButton(
              icon: const Icon(Icons.my_location),
              onPressed: _goToMyLocation,
            ),
          ],
        ),
        body: Consumer<LocationProvider>(
          builder: (context, locationProvider, child) {
            if (locationProvider.isLoading) {
              return const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryGold),
                ),
              );
            }

            if (locationProvider.hasError) {
              return Center(
                child: CuteCard(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.location_off,
                        size: 64,
                        color: AppColors.errorRed,
                      ),
                      const SizedBox(height: AppSizes.spacing16),
                      Text(
                        '无法获取位置信息',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: AppSizes.spacing8),
                      Text(
                        locationProvider.errorMessage ?? '请检查位置权限设置',
                        style: Theme.of(context).textTheme.bodyMedium,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: AppSizes.spacing16),
                      CuteButton(
                        text: '重新获取位置',
                        onPressed: () => locationProvider.getCurrentLocation(),
                        style: CuteButtonStyle.primary,
                      ),
                    ],
                  ),
                ),
              );
            }

            return Stack(
              children: [
                // 地图主体
                _buildMap(locationProvider),
                
                // 搜索栏
                Positioned(
                  top: AppSizes.spacing16,
                  left: AppSizes.spacing16,
                  right: AppSizes.spacing16,
                  child: _buildSearchBar(),
                ),
                
                // 底部任务列表
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: _buildBottomSheet(),
                ),
              ],
            );
          },
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: _showQRScanner,
          child: const Icon(Icons.qr_code_scanner),
          tooltip: 'AR扫描',
        ),
      ),
    );
  }

  /// 构建地图
  Widget _buildMap(LocationProvider locationProvider) {
    final position = locationProvider.currentPosition;
    if (position == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return GoogleMap(
      onMapCreated: (GoogleMapController controller) {
        _mapController = controller;
      },
      initialCameraPosition: CameraPosition(
        target: LatLng(position.latitude, position.longitude),
        zoom: 15.0,
      ),
      markers: _markers,
      myLocationEnabled: true,
      myLocationButtonEnabled: false,
      zoomControlsEnabled: false,
      mapToolbarEnabled: false,
    );
  }

  /// 构建搜索栏
  Widget _buildSearchBar() {
    return CuteCard(
      child: Row(
        children: [
          const Icon(Icons.search, color: AppColors.textHint),
          const SizedBox(width: AppSizes.spacing8),
          const Expanded(
            child: TextField(
              decoration: InputDecoration(
                hintText: '搜索地点或任务...',
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.tune, color: AppColors.primaryOrange),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
    );
  }

  /// 构建底部任务列表
  Widget _buildBottomSheet() {
    return Container(
      height: 200,
      decoration: const BoxDecoration(
        color: AppColors.cardWhite,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 拖拽指示器
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: AppSizes.spacing8),
            decoration: BoxDecoration(
              color: AppColors.border,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // 标题
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSizes.spacing16),
            child: Row(
              children: [
                const Icon(Icons.assignment, color: AppColors.primaryGold),
                const SizedBox(width: AppSizes.spacing8),
                Text(
                  '附近任务',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // TODO: 查看全部任务
                  },
                  child: const Text('查看全部'),
                ),
              ],
            ),
          ),
          
          // 任务列表
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: AppSizes.spacing16),
              itemCount: 3, // TODO: 从数据源获取
              itemBuilder: (context, index) {
                return _buildTaskItem(index);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建任务项
  Widget _buildTaskItem(int index) {
    final tasks = [
      {'title': '📍 星巴克签到', 'reward': '20', 'distance': '50m'},
      {'title': '🍔 麦当劳消费', 'reward': '50', 'distance': '120m'},
      {'title': '📸 公园拍照', 'reward': '30', 'distance': '200m'},
    ];
    
    final task = tasks[index];
    
    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.spacing8),
      padding: const EdgeInsets.all(AppSizes.spacing12),
      decoration: BoxDecoration(
        color: AppColors.cardWhite,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              gradient: AppColors.goldGradient,
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(Icons.star, color: Colors.white),
          ),
          const SizedBox(width: AppSizes.spacing12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  task['title']!,
                  style: Theme.of(context).textTheme.titleSmall,
                ),
                Text(
                  '距离: ${task['distance']}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
          Column(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSizes.spacing8,
                  vertical: AppSizes.spacing4,
                ),
                decoration: BoxDecoration(
                  color: AppColors.primaryGold.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '+${task['reward']} 💎',
                  style: const TextStyle(
                    color: AppColors.primaryGold,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
              const SizedBox(height: AppSizes.spacing4),
              const Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: AppColors.textHint,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 显示搜索对话框
  void _showSearchDialog() {
    // TODO: 实现搜索功能
  }

  /// 显示筛选对话框
  void _showFilterDialog() {
    // TODO: 实现筛选功能
  }

  /// 显示二维码扫描器
  void _showQRScanner() {
    // TODO: 实现AR扫描功能
  }

  /// 回到我的位置
  void _goToMyLocation() {
    final locationProvider = Provider.of<LocationProvider>(context, listen: false);
    final position = locationProvider.currentPosition;
    if (position != null && _mapController != null) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLng(
          LatLng(position.latitude, position.longitude),
        ),
      );
    }
  }
}
