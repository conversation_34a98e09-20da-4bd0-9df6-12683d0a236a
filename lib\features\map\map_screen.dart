import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_strings.dart';
import '../../core/constants/app_sizes.dart';
import '../../shared/providers/location_provider.dart';
import '../../shared/widgets/cute_card.dart';
import '../../shared/widgets/cute_button.dart';

/// 探险地图页面
/// 显示用户位置和附近的任务点
class MapScreen extends StatefulWidget {
  const MapScreen({super.key});

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  GoogleMapController? _mapController;
  final Set<Marker> _markers = {};
  
  @override
  void initState() {
    super.initState();
    _initializeMap();
  }

  /// 初始化地图
  void _initializeMap() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final locationProvider = Provider.of<LocationProvider>(context, listen: false);
      locationProvider.getCurrentLocation();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: AppColors.backgroundGradient,
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          title: const Text('🗺️ 探险地图'),
          backgroundColor: Colors.transparent,
          actions: [
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: _showSearchDialog,
            ),
            IconButton(
              icon: const Icon(Icons.my_location),
              onPressed: _goToMyLocation,
            ),
          ],
        ),
        body: Consumer<LocationProvider>(
          builder: (context, locationProvider, child) {
            if (locationProvider.isLoading) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 旋转的探险罗盘
                    TweenAnimationBuilder(
                      duration: const Duration(seconds: 2),
                      tween: Tween<double>(begin: 0, end: 1),
                      builder: (context, double value, child) {
                        return Transform.rotate(
                          angle: value * 2 * 3.14159,
                          child: Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              gradient: AppColors.goldGradient,
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.primaryGold.withOpacity(0.4),
                                  blurRadius: 16,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.explore,
                              size: 40,
                              color: Colors.white,
                            ),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: AppSizes.spacing24),

                    Text(
                      '🧭 正在寻找你的位置...',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppColors.primaryGold,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppSizes.spacing8),

                    Text(
                      '探险罗盘正在校准中',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: AppSizes.spacing16),

                    // 进度条
                    Container(
                      width: 200,
                      height: 4,
                      decoration: BoxDecoration(
                        color: AppColors.border,
                        borderRadius: BorderRadius.circular(2),
                      ),
                      child: TweenAnimationBuilder(
                        duration: const Duration(seconds: 3),
                        tween: Tween<double>(begin: 0, end: 1),
                        builder: (context, double value, child) {
                          return FractionallySizedBox(
                            widthFactor: value,
                            child: Container(
                              decoration: BoxDecoration(
                                gradient: AppColors.goldGradient,
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              );
            }

            if (locationProvider.hasError) {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(AppSizes.spacing24),
                  child: CuteCard(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // 可爱的探险家角色
                        Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            gradient: AppColors.goldGradient,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.primaryGold.withOpacity(0.3),
                                blurRadius: 20,
                                offset: const Offset(0, 8),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.explore,
                            size: 60,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: AppSizes.spacing24),

                        Text(
                          '🗺️ 探险需要定位魔法',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            color: AppColors.primaryGold,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: AppSizes.spacing12),

                        Text(
                          '小探险家，我们需要知道你的位置\n才能为你寻找附近的神秘宝藏哦！',
                          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: AppColors.textSecondary,
                            height: 1.5,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: AppSizes.spacing24),

                        // 权限说明卡片
                        Container(
                          padding: const EdgeInsets.all(AppSizes.spacing16),
                          decoration: BoxDecoration(
                            color: AppColors.skyBlue.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: AppColors.skyBlue.withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.security,
                                color: AppColors.skyBlue,
                                size: 24,
                              ),
                              const SizedBox(width: AppSizes.spacing12),
                              Expanded(
                                child: Text(
                                  '我们承诺保护你的隐私安全\n位置信息仅用于游戏功能',
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: AppColors.skyBlue,
                                    height: 1.4,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: AppSizes.spacing24),

                        // 操作按钮
                        Row(
                          children: [
                            Expanded(
                              child: CuteButton(
                                text: '🔄 重新尝试',
                                onPressed: () => locationProvider.getCurrentLocation(),
                                style: CuteButtonStyle.outline,
                              ),
                            ),
                            const SizedBox(width: AppSizes.spacing12),
                            Expanded(
                              child: CuteButton(
                                text: '⚙️ 打开设置',
                                onPressed: () => locationProvider.openAppSettings(),
                                style: CuteButtonStyle.primary,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }

            return Stack(
              children: [
                // 地图主体
                _buildMap(locationProvider),
                
                // 搜索栏
                Positioned(
                  top: AppSizes.spacing16,
                  left: AppSizes.spacing16,
                  right: AppSizes.spacing16,
                  child: _buildSearchBar(),
                ),
                
                // 底部任务列表
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: _buildBottomSheet(),
                ),
              ],
            );
          },
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: _showQRScanner,
          child: const Icon(Icons.qr_code_scanner),
          tooltip: 'AR扫描',
        ),
      ),
    );
  }

  /// 构建地图
  Widget _buildMap(LocationProvider locationProvider) {
    final position = locationProvider.currentPosition;
    if (position == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return GoogleMap(
      onMapCreated: (GoogleMapController controller) {
        _mapController = controller;
      },
      initialCameraPosition: CameraPosition(
        target: LatLng(position.latitude, position.longitude),
        zoom: 15.0,
      ),
      markers: _markers,
      myLocationEnabled: true,
      myLocationButtonEnabled: false,
      zoomControlsEnabled: false,
      mapToolbarEnabled: false,
    );
  }

  /// 构建搜索栏
  Widget _buildSearchBar() {
    return CuteCard(
      child: Row(
        children: [
          const Icon(Icons.search, color: AppColors.textHint),
          const SizedBox(width: AppSizes.spacing8),
          const Expanded(
            child: TextField(
              decoration: InputDecoration(
                hintText: '搜索地点或任务...',
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.tune, color: AppColors.primaryOrange),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
    );
  }

  /// 构建底部任务列表
  Widget _buildBottomSheet() {
    return Container(
      height: 200,
      decoration: const BoxDecoration(
        color: AppColors.cardWhite,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 拖拽指示器
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: AppSizes.spacing8),
            decoration: BoxDecoration(
              color: AppColors.border,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // 标题
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSizes.spacing16),
            child: Row(
              children: [
                const Icon(Icons.assignment, color: AppColors.primaryGold),
                const SizedBox(width: AppSizes.spacing8),
                Text(
                  '附近任务',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // TODO: 查看全部任务
                  },
                  child: const Text('查看全部'),
                ),
              ],
            ),
          ),
          
          // 任务列表
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: AppSizes.spacing16),
              itemCount: 3, // TODO: 从数据源获取
              itemBuilder: (context, index) {
                return _buildTaskItem(index);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建任务项
  Widget _buildTaskItem(int index) {
    final tasks = [
      {
        'title': '☕ 星巴克探宝',
        'subtitle': '在星巴克完成签到任务',
        'reward': '20',
        'distance': '50m',
        'type': 'checkin',
        'difficulty': '简单',
        'icon': '☕',
        'rarity': 'common'
      },
      {
        'title': '🍔 美食猎人',
        'subtitle': '在麦当劳消费并拍照',
        'reward': '50',
        'distance': '120m',
        'type': 'purchase',
        'difficulty': '普通',
        'icon': '🍔',
        'rarity': 'rare'
      },
      {
        'title': '📸 风景收集家',
        'subtitle': '在公园拍摄美丽风景',
        'reward': '30',
        'distance': '200m',
        'type': 'photo',
        'difficulty': '简单',
        'icon': '📸',
        'rarity': 'common'
      },
    ];

    final task = tasks[index];
    final rarityColor = _getRarityColor(task['rarity']!);
    final difficultyColor = _getDifficultyColor(task['difficulty']!);

    return Container(
      margin: const EdgeInsets.only(bottom: AppSizes.spacing12),
      child: CuteCard(
        onTap: () => _showTaskDetail(task),
        child: Row(
          children: [
            // 任务图标
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                gradient: _getTaskGradient(task['rarity']!),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: rarityColor.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  task['icon']!,
                  style: const TextStyle(fontSize: 24),
                ),
              ),
            ),
            const SizedBox(width: AppSizes.spacing16),

            // 任务信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          task['title']!,
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                      ),
                      // 稀有度标签
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: rarityColor.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: rarityColor.withOpacity(0.5),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          task['rarity']! == 'common' ? '普通' : '稀有',
                          style: TextStyle(
                            color: rarityColor,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppSizes.spacing4),

                  Text(
                    task['subtitle']!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: AppSizes.spacing8),

                  // 距离和难度
                  Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 14,
                        color: AppColors.textHint,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        task['distance']!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textHint,
                        ),
                      ),
                      const SizedBox(width: AppSizes.spacing12),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: difficultyColor.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Text(
                          task['difficulty']!,
                          style: TextStyle(
                            color: difficultyColor,
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // 奖励和箭头
            Column(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSizes.spacing10,
                    vertical: AppSizes.spacing6,
                  ),
                  decoration: BoxDecoration(
                    gradient: AppColors.goldGradient,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primaryGold.withOpacity(0.3),
                        blurRadius: 6,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text(
                        '💎',
                        style: TextStyle(fontSize: 12),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '+${task['reward']}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: AppSizes.spacing8),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: AppColors.textHint,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 显示搜索对话框
  void _showSearchDialog() {
    // TODO: 实现搜索功能
  }

  /// 显示筛选对话框
  void _showFilterDialog() {
    // TODO: 实现筛选功能
  }

  /// 显示二维码扫描器
  void _showQRScanner() {
    // TODO: 实现AR扫描功能
  }

  /// 回到我的位置
  void _goToMyLocation() {
    final locationProvider = Provider.of<LocationProvider>(context, listen: false);
    final position = locationProvider.currentPosition;
    if (position != null && _mapController != null) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLng(
          LatLng(position.latitude, position.longitude),
        ),
      );
    }
  }

  /// 获取稀有度颜色
  Color _getRarityColor(String rarity) {
    switch (rarity) {
      case 'common':
        return AppColors.rarityCommon;
      case 'rare':
        return AppColors.rarityRare;
      case 'legendary':
        return AppColors.rarityLegendary;
      case 'limited':
        return AppColors.rarityLimited;
      default:
        return AppColors.rarityCommon;
    }
  }

  /// 获取难度颜色
  Color _getDifficultyColor(String difficulty) {
    switch (difficulty) {
      case '简单':
        return AppColors.limeGreen;
      case '普通':
        return AppColors.primaryOrange;
      case '困难':
        return AppColors.errorRed;
      default:
        return AppColors.limeGreen;
    }
  }

  /// 获取任务渐变
  LinearGradient _getTaskGradient(String rarity) {
    switch (rarity) {
      case 'common':
        return LinearGradient(
          colors: [AppColors.rarityCommon, AppColors.rarityCommon.withOpacity(0.7)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'rare':
        return AppColors.blueGradient;
      case 'legendary':
        return AppColors.purpleGradient;
      case 'limited':
        return AppColors.goldGradient;
      default:
        return LinearGradient(
          colors: [AppColors.rarityCommon, AppColors.rarityCommon.withOpacity(0.7)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
    }
  }

  /// 显示任务详情
  void _showTaskDetail(Map<String, String> task) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: AppColors.cardWhite,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
        ),
        child: Column(
          children: [
            // 拖拽指示器
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: AppColors.border,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // 任务头部
            Padding(
              padding: const EdgeInsets.all(AppSizes.spacing20),
              child: Row(
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      gradient: _getTaskGradient(task['rarity']!),
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: _getRarityColor(task['rarity']!).withOpacity(0.3),
                          blurRadius: 12,
                          offset: const Offset(0, 6),
                        ),
                      ],
                    ),
                    child: Center(
                      child: Text(
                        task['icon']!,
                        style: const TextStyle(fontSize: 32),
                      ),
                    ),
                  ),
                  const SizedBox(width: AppSizes.spacing16),

                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          task['title']!,
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        const SizedBox(height: AppSizes.spacing8),
                        Text(
                          task['subtitle']!,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // 任务信息
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: AppSizes.spacing20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 奖励信息
                    CuteCard(
                      child: Row(
                        children: [
                          const Text('💎', style: TextStyle(fontSize: 24)),
                          const SizedBox(width: AppSizes.spacing12),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '任务奖励',
                                style: Theme.of(context).textTheme.titleSmall,
                              ),
                              Text(
                                '+${task['reward']} 宝石币',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: AppColors.primaryGold,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: AppSizes.spacing16),

                    // 任务要求
                    Text(
                      '📋 任务要求',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppSizes.spacing8),

                    Text(
                      _getTaskRequirement(task['type']!),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.textSecondary,
                        height: 1.5,
                      ),
                    ),

                    const Spacer(),

                    // 开始任务按钮
                    Padding(
                      padding: const EdgeInsets.only(bottom: AppSizes.spacing20),
                      child: CuteButton(
                        text: '🚀 开始探险',
                        onPressed: () {
                          Navigator.pop(context);
                          _startTask(task);
                        },
                        style: CuteButtonStyle.primary,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取任务要求描述
  String _getTaskRequirement(String type) {
    switch (type) {
      case 'checkin':
        return '1. 前往指定地点\n2. 在店内完成签到\n3. 获得宝石币奖励';
      case 'purchase':
        return '1. 前往指定商店\n2. 完成任意消费\n3. 拍照上传小票\n4. 获得宝石币奖励';
      case 'photo':
        return '1. 前往指定地点\n2. 拍摄符合要求的照片\n3. 上传照片等待审核\n4. 获得宝石币奖励';
      default:
        return '按照任务提示完成相应操作即可获得奖励';
    }
  }

  /// 开始任务
  void _startTask(Map<String, String> task) {
    // TODO: 实现任务开始逻辑
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('🎯 已接受任务：${task['title']}'),
        backgroundColor: AppColors.primaryGold,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
}
