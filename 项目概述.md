# Cursor AI 开发指令：宝藏奇缘（Treasure Odyssey）

## 🎯 项目概述
你正在开发一款名为"宝藏奇缘：现实秘境"的Flutter移动游戏。这是一款基于GPS定位的现实世界探险游戏，玩家通过完成现实中的任务来获得成就卡片。

## 📋 严格遵循的开发规范

### 项目结构
```
lib/
├── main.dart
├── app.dart
├── core/
│   ├── constants/
│   ├── theme/
│   ├── utils/
│   └── services/
├── models/
├── providers/
├── screens/
├── widgets/
└── data/
```

### 代码规范
1. **命名规范**
   - 文件名：snake_case
   - 类名：PascalCase
   - 变量/方法：camelCase
   - 常量：UPPER_SNAKE_CASE

2. **注释要求**
   - 所有公共方法必须有中文注释
   - 复杂逻辑需要行内注释说明
   - 每个Screen类顶部说明功能用途

### UI设计严格要求

#### 颜色系统（必须使用）
```dart
class AppColors {
  // 主色调
  static const Color treasureGold = Color(0xFFFFD700);
  static const Color mysteryBlue = Color(0xFF1E3A8A);
  static const Color emeraldGreen = Color(0xFF10B981);
  
  // 辅助色
  static const Color backgroundWhite = Color(0xFFFAFAFA);
  static const Color textBlack = Color(0xFF1F2937);
  static const Color borderGray = Color(0xFFE5E7EB);
  static const Color warningRed = Color(0xFFEF4444);
}
```

#### 组件规范（严格执行）
```dart
// 按钮样式
ElevatedButton(
  style: ElevatedButton.styleFrom(
    backgroundColor: AppColors.treasureGold,
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    minimumSize: Size(double.infinity, 48),
  ),
)

// 卡片样式
Card(
  elevation: 4,
  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
  child: Container(padding: EdgeInsets.all(16))
)
```

## 🏗️ 必须实现的核心功能

### 1. 首页 (HomeScreen)
- 顶部显示玩家信息（头像、昵称、等级）
- 中间展示每日任务卡片
- 底部导航栏：探险、成就、个人中心

### 2. 地图探险页 (MapScreen) 
- 使用google_maps_flutter显示地图
- 集成geolocator获取GPS位置
- 在地图上标记任务点（不同颜色表示不同类型）
- 添加"发现模式"按钮（准备AR功能）

### 3. 成就系统 (AchievementScreen)
- GridView展示成就卡片
- 卡片包含：图标、标题、描述、获得时间
- 不同稀有度用不同边框颜色区分
- 支持筛选和搜索功能

### 4. 任务详情页 (QuestDetailScreen)
- 显示任务描述和要求
- 实时距离计算和导航提示
- 任务进度跟踪
- 完成任务的拍照/签到功能

## 🔧 技术实现要求

### 状态管理
使用Provider模式，创建以下Provider：
```dart
- GameProvider: 游戏全局状态
- PlayerProvider: 玩家数据管理  
- QuestProvider: 任务状态管理
- LocationProvider: 位置服务管理
```

### 数据持久化
```dart
// 使用SharedPreferences存储用户设置
// 使用sqflite存储游戏数据
class DatabaseHelper {
  // 玩家表
  // 成就表  
  // 任务表
}
```

### 必需依赖包
在pubspec.yaml中添加：
```yaml
dependencies:
  flutter:
    sdk: flutter
  provider: ^6.1.1
  geolocator: ^10.1.0
  google_maps_flutter: ^2.5.0
  camera: ^0.10.5
  shared_preferences: ^2.2.2
  sqflite: ^2.3.0
  cached_network_image: ^3.3.0
  http: ^1.1.0
  permission_handler: ^11.1.0
  image_picker: ^1.0.4
```

## 🎨 UI实现细节

### 主题配置
```dart
ThemeData(
  primaryColor: AppColors.treasureGold,
  scaffoldBackgroundColor: AppColors.backgroundWhite,
  appBarTheme: AppBarTheme(
    backgroundColor: AppColors.mysteryBlue,
    foregroundColor: Colors.white,
    elevation: 0,
  ),
  cardTheme: CardTheme(
    elevation: 4,
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
  ),
)
```

### 动画效果
- 页面转场使用Material的默认动画
- 卡片点击添加缩放动画
- 加载状态使用CircularProgressIndicator

## ⚠️ 重要开发约定

1. **权限处理**：地图和相机功能必须先检查权限
2. **错误处理**：所有异步操作必须包含try-catch
3. **响应式设计**：支持不同屏幕尺寸，使用MediaQuery
4. **国际化准备**：所有文本使用常量定义，便于后续多语言支持
5. **性能优化**：列表使用ListView.builder，图片使用缓存

## 🚀 开发优先级

### Phase 1 (立即开始)
1. 搭建基础项目结构
2. 实现主页和导航
3. 创建基础的Player和Achievement模型
4. 实现简单的成就展示页面

### Phase 2 
1. 集成地图功能
2. 实现GPS定位
3. 添加任务系统基础功能
4. 实现拍照任务功能

### Phase 3
1. 完善UI动画效果
2. 添加社交功能框架
3. 优化性能和用户体验
4. 准备发布版本

## 📝 代码质量要求

- 每个功能完成后进行自测
- 确保没有编译错误和警告
- 遵循Flutter官方代码规范
- 关键功能添加单元测试

**请严格按照以上规范开发，优先实现Phase 1的功能，确保代码质量和用户体验！**