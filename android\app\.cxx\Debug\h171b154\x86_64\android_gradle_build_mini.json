{"buildFiles": ["D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\project\\treasure_odyssey\\android\\app\\.cxx\\Debug\\h171b154\\x86_64", "clean"]], "buildTargetsCommandComponents": ["D:\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\project\\treasure_odyssey\\android\\app\\.cxx\\Debug\\h171b154\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}