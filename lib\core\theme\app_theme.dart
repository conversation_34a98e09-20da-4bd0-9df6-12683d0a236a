import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_sizes.dart';

/// 应用主题配置
/// 统一管理应用的视觉风格
class AppTheme {
  /// 获取应用主题数据
  static ThemeData get themeData {
    return ThemeData(
      // 基础颜色方案
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.mysteryBlue,
        primary: AppColors.mysteryBlue,
        secondary: AppColors.treasureGold,
        surface: AppColors.backgroundWhite,
        error: AppColors.warningRed,
      ),
      
      // 应用栏主题
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.mysteryBlue,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
      
      // 按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.treasureGold,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSizes.buttonRadius),
          ),
          minimumSize: const Size(double.infinity, AppSizes.buttonHeight),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      
      // 卡片主题
      cardTheme: CardTheme(
        elevation: AppSizes.cardElevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSizes.cardRadius),
        ),
        color: Colors.white,
        margin: const EdgeInsets.all(AppSizes.spacing8),
      ),
      
      // 输入框主题
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSizes.inputRadius),
          borderSide: const BorderSide(color: AppColors.borderGray),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSizes.inputRadius),
          borderSide: const BorderSide(color: AppColors.borderGray),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSizes.inputRadius),
          borderSide: const BorderSide(color: AppColors.mysteryBlue, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppSizes.inputRadius),
          borderSide: const BorderSide(color: AppColors.warningRed),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSizes.spacing16,
          vertical: AppSizes.spacing12,
        ),
      ),
      
      // 文本主题
      textTheme: const TextTheme(
        headlineLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: AppColors.textBlack,
        ),
        headlineMedium: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: AppColors.textBlack,
        ),
        headlineSmall: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: AppColors.textBlack,
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          color: AppColors.textBlack,
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          color: AppColors.textBlack,
        ),
        bodySmall: TextStyle(
          fontSize: 12,
          color: AppColors.textBlack,
        ),
      ),
      
      // 底部导航栏主题
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: Colors.white,
        selectedItemColor: AppColors.mysteryBlue,
        unselectedItemColor: AppColors.borderGray,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),
      
      // 使用Material Design 3
      useMaterial3: true,
    );
  }
  
  // 私有构造函数，防止实例化
  AppTheme._();
}
