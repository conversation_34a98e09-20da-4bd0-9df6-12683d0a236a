import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/constants/app_colors.dart';
import '../../core/constants/app_strings.dart';
import '../../core/constants/app_sizes.dart';
import '../../providers/player_provider.dart';
import '../../providers/achievement_provider.dart';
import '../../providers/game_provider.dart';

/// 个人资料页面 - 显示玩家信息和设置
class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final TextEditingController _nicknameController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeProfile();
  }

  @override
  void dispose() {
    _nicknameController.dispose();
    super.dispose();
  }

  /// 初始化个人资料
  void _initializeProfile() {
    final playerProvider = context.read<PlayerProvider>();
    if (playerProvider.currentPlayer != null) {
      _nicknameController.text = playerProvider.currentPlayer!.nickname;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.profileTitle),
        backgroundColor: AppColors.mysteryBlue,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showSettings,
            tooltip: AppStrings.profileSettings,
          ),
        ],
      ),
      body: Consumer3<PlayerProvider, AchievementProvider, GameProvider>(
        builder: (context, playerProvider, achievementProvider, gameProvider, child) {
          final player = playerProvider.currentPlayer;
          
          if (player == null) {
            return const Center(
              child: Text('玩家数据加载中...'),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppSizes.spacing16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 玩家头像和基本信息
                _buildPlayerHeader(player),
                
                const SizedBox(height: AppSizes.spacing24),
                
                // 等级和经验
                _buildLevelCard(player),
                
                const SizedBox(height: AppSizes.spacing16),
                
                // 成就统计
                _buildAchievementStats(achievementProvider),
                
                const SizedBox(height: AppSizes.spacing16),
                
                // 游戏统计
                _buildGameStats(player),
                
                const SizedBox(height: AppSizes.spacing16),
                
                // 操作按钮
                _buildActionButtons(playerProvider),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 构建玩家头像和基本信息
  Widget _buildPlayerHeader(dynamic player) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.cardPadding),
        child: Row(
          children: [
            // 头像
            GestureDetector(
              onTap: _changeAvatar,
              child: CircleAvatar(
                radius: 40,
                backgroundColor: AppColors.treasureGold,
                backgroundImage: player.avatarUrl != null 
                    ? NetworkImage(player.avatarUrl!) 
                    : null,
                child: player.avatarUrl == null
                    ? Text(
                        player.nickname.isNotEmpty ? player.nickname[0] : '?',
                        style: const TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      )
                    : null,
              ),
            ),
            const SizedBox(width: AppSizes.spacing16),
            
            // 基本信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          player.nickname,
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.edit, size: AppSizes.iconSmall),
                        onPressed: _editNickname,
                        tooltip: '编辑昵称',
                      ),
                    ],
                  ),
                  const SizedBox(height: AppSizes.spacing4),
                  Text(
                    'ID: ${player.id}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.borderGray,
                    ),
                  ),
                  const SizedBox(height: AppSizes.spacing4),
                  Text(
                    '注册时间: ${_formatDate(player.createdAt)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.borderGray,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建等级卡片
  Widget _buildLevelCard(dynamic player) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.cardPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '等级信息',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: AppSizes.spacing16),
            
            Row(
              children: [
                // 等级
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: AppColors.mysteryBlue,
                    borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '${player.level}',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const Text(
                        '等级',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: AppSizes.spacing16),
                
                // 经验进度
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '经验值',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: AppSizes.spacing8),
                      LinearProgressIndicator(
                        value: player.experienceProgress,
                        backgroundColor: AppColors.borderGray,
                        valueColor: const AlwaysStoppedAnimation<Color>(
                          AppColors.treasureGold,
                        ),
                      ),
                      const SizedBox(height: AppSizes.spacing4),
                      Text(
                        '${player.experience}/${player.experienceToNextLevel}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建成就统计
  Widget _buildAchievementStats(AchievementProvider achievementProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.cardPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '成就统计',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: AppSizes.spacing16),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    '总成就',
                    '${achievementProvider.allAchievements.length}',
                    Icons.emoji_events,
                    AppColors.mysteryBlue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    '已完成',
                    '${achievementProvider.completedAchievements.length}',
                    Icons.check_circle,
                    AppColors.emeraldGreen,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    '完成率',
                    '${achievementProvider.completionPercentage.toStringAsFixed(1)}%',
                    Icons.trending_up,
                    AppColors.treasureGold,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建游戏统计
  Widget _buildGameStats(dynamic player) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.cardPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '游戏统计',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: AppSizes.spacing16),
            
            _buildStatRow('最后登录', _formatDate(player.lastLoginAt)),
            const SizedBox(height: AppSizes.spacing8),
            _buildStatRow('游戏天数', _calculateDaysPlayed(player.createdAt)),
            const SizedBox(height: AppSizes.spacing8),
            _buildStatRow('成就进度', '${player.completedAchievements}/${player.totalAchievements}'),
          ],
        ),
      ),
    );
  }

  /// 构建统计项目
  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: AppSizes.iconLarge),
        const SizedBox(height: AppSizes.spacing8),
        Text(
          value,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// 构建统计行
  Widget _buildStatRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(PlayerProvider playerProvider) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _exportData,
            icon: const Icon(Icons.download),
            label: const Text('导出数据'),
          ),
        ),
        const SizedBox(height: AppSizes.spacing8),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () => _showLogoutDialog(playerProvider),
            icon: const Icon(Icons.logout),
            label: const Text('退出登录'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.warningRed,
              side: const BorderSide(color: AppColors.warningRed),
            ),
          ),
        ),
      ],
    );
  }

  /// 编辑昵称
  void _editNickname() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('编辑昵称'),
        content: TextField(
          controller: _nicknameController,
          decoration: const InputDecoration(
            labelText: '新昵称',
            hintText: '请输入新的昵称',
          ),
          maxLength: 20,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(AppStrings.buttonCancel),
          ),
          TextButton(
            onPressed: () async {
              final newNickname = _nicknameController.text.trim();
              if (newNickname.isNotEmpty) {
                Navigator.of(context).pop();
                final playerProvider = context.read<PlayerProvider>();
                await playerProvider.updateNickname(newNickname);
                
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('昵称更新成功'),
                      backgroundColor: AppColors.emeraldGreen,
                    ),
                  );
                }
              }
            },
            child: const Text(AppStrings.buttonConfirm),
          ),
        ],
      ),
    );
  }

  /// 更换头像
  void _changeAvatar() {
    // TODO: 实现头像更换功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('头像更换功能开发中...'),
      ),
    );
  }

  /// 显示设置
  void _showSettings() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(AppSizes.radiusLarge)),
      ),
      builder: (context) => _buildSettingsSheet(),
    );
  }

  /// 构建设置底部表单
  Widget _buildSettingsSheet() {
    return Consumer<GameProvider>(
      builder: (context, gameProvider, child) {
        return Container(
          padding: const EdgeInsets.all(AppSizes.spacing16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 拖拽指示器
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: AppColors.borderGray,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              const SizedBox(height: AppSizes.spacing16),
              
              Text(
                '游戏设置',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: AppSizes.spacing16),
              
              // 声音设置
              SwitchListTile(
                title: const Text('声音效果'),
                subtitle: const Text('开启/关闭游戏声音'),
                value: gameProvider.soundEnabled,
                onChanged: gameProvider.setSoundEnabled,
                activeColor: AppColors.treasureGold,
              ),
              
              // 震动设置
              SwitchListTile(
                title: const Text('震动反馈'),
                subtitle: const Text('开启/关闭震动反馈'),
                value: gameProvider.vibrationEnabled,
                onChanged: gameProvider.setVibrationEnabled,
                activeColor: AppColors.treasureGold,
              ),
              
              // 音乐音量
              ListTile(
                title: const Text('音乐音量'),
                subtitle: Slider(
                  value: gameProvider.musicVolume,
                  onChanged: gameProvider.setMusicVolume,
                  activeColor: AppColors.treasureGold,
                ),
              ),
              
              // 重置设置按钮
              const SizedBox(height: AppSizes.spacing16),
              SizedBox(
                width: double.infinity,
                child: OutlinedButton(
                  onPressed: gameProvider.resetSettings,
                  child: const Text('重置设置'),
                ),
              ),
              
              // 安全区域底部间距
              SizedBox(height: MediaQuery.of(context).padding.bottom),
            ],
          ),
        );
      },
    );
  }

  /// 导出数据
  void _exportData() {
    // TODO: 实现数据导出功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('数据导出功能开发中...'),
      ),
    );
  }

  /// 显示退出登录对话框
  void _showLogoutDialog(PlayerProvider playerProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('退出登录'),
        content: const Text('确定要退出登录吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(AppStrings.buttonCancel),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              playerProvider.logout();
              // TODO: 导航到登录页面
            },
            child: const Text(AppStrings.buttonConfirm),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.warningRed,
            ),
          ),
        ],
      ),
    );
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 计算游戏天数
  String _calculateDaysPlayed(DateTime createdAt) {
    final days = DateTime.now().difference(createdAt).inDays;
    return '$days 天';
  }
}
