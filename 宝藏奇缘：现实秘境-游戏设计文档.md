# 宝藏奇缘：现实秘境 (Treasure Odyssey: Realms of Mystery)
## 游戏设计文档

### 🎮 游戏概述
一款基于现实世界探险的移动休闲游戏，玩家通过GPS定位在真实世界中完成任务，收集成就卡片，体验沉浸式的现实增强冒险。

---

## 📱 核心功能模块

### 1. 主界面 (Home Screen)
- **探险地图** - 显示玩家当前位置和周边任务点
- **成就收藏册** - 展示已获得的成就卡片
- **玩家信息** - 等级、经验值、探险统计
- **快速导航** - 任务、商店、设置入口

### 2. 地图探险模块 (Map Explorer)
- **实时GPS地图** - 显示玩家位置和附近兴趣点
- **任务标记** - 不同类型任务的视觉标识
- **发现模式** - AR扫描识别现实中的隐藏宝藏
- **路径规划** - 优化探险路线建议

### 3. 任务系统 (Quest System)
- **位置任务** - 到达特定地点完成签到
- **拍照任务** - 拍摄指定目标或场景
- **步数挑战** - 基于运动数据的健康任务
- **时间限定** - 特殊活动和限时挑战

### 4. 成就卡片系统 (Achievement Cards)
- **卡片收集** - 完成任务获得精美成就卡
- **稀有度分级** - 普通、稀有、史诗、传说级别
- **卡片详情** - 获得时间、地点、故事背景
- **成就册** - 分类展示和进度追踪

### 5. 社交功能 (Social Features)
- **好友系统** - 添加好友，查看探险进度
- **排行榜** - 全球/本地探险者排名
- **分享功能** - 分享成就和探险足迹
- **队伍探险** - 多人协作完成团队任务

### 6. 个人中心 (Profile)
- **探险者档案** - 个人信息和成就统计
- **背包系统** - 道具和收藏品管理
- **设置中心** - 游戏偏好和隐私设置

---

## 🎨 UI视觉风格设计

### 主题风格
- **神秘冒险风** - 融合现代科技与古典探险元素
- **暖色调主导** - 金黄、深蓝、翡翠绿的配色方案
- **渐变效果** - 丰富的光影和材质纹理

### 设计元素
- **图标风格** - 扁平化与拟物化结合，线条流畅
- **字体选择** - 中文：思源黑体 / 英文：Roboto
- **卡片设计** - 圆角矩形，阴影层次感
- **动画效果** - 平滑的过渡和微交互

### 颜色规范
```
主色调：
- 宝藏金: #FFD700
- 神秘蓝: #1E3A8A
- 翡翠绿: #10B981

辅助色：
- 背景白: #FAFAFA
- 文字黑: #1F2937
- 边框灰: #E5E7EB
- 警告红: #EF4444
```

### 组件规范
- **按钮** - 圆角12px，高度48px
- **卡片** - 圆角16px，阴影elevation 4
- **输入框** - 圆角8px，边框1px
- **导航栏** - 高度64px，底部安全区适配

---

## 🏗️ 技术架构

### 状态管理
- **Provider/Riverpod** - 全局状态管理
- **SharedPreferences** - 本地数据持久化
- **SQLite** - 成就和任务数据存储

### 核心依赖
- **geolocator** - GPS定位服务
- **google_maps** - 地图显示
- **camera** - 拍照功能
- **http** - 网络请求
- **cached_network_image** - 图片缓存

### 数据模型
```dart
// 玩家数据
class Player {
  String id;
  String nickname;
  int level;
  int experience;
  List<Achievement> achievements;
}

// 成就卡片
class Achievement {
  String id;
  String title;
  String description;
  Rarity rarity;
  DateTime unlockedAt;
  String imageUrl;
}

// 任务
class Quest {
  String id;
  QuestType type;
  String title;
  String description;
  Location targetLocation;
  List<String> requirements;
  Achievement reward;
}
```