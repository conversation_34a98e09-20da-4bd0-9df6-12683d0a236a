/// 成就数据模型
/// 存储成就卡片的信息和状态
class Achievement {
  /// 成就ID
  final String id;
  
  /// 成就标题
  final String title;
  
  /// 成就描述
  final String description;
  
  /// 成就图标URL
  final String iconUrl;
  
  /// 成就类型
  final AchievementType type;
  
  /// 成就稀有度
  final AchievementRarity rarity;
  
  /// 是否已完成
  final bool isCompleted;
  
  /// 完成时间
  final DateTime? completedAt;
  
  /// 获得经验值
  final int experienceReward;
  
  /// 完成条件描述
  final String requirement;
  
  /// 当前进度
  final int currentProgress;
  
  /// 目标进度
  final int targetProgress;
  
  /// 构造函数
  const Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.iconUrl,
    required this.type,
    required this.rarity,
    required this.isCompleted,
    this.completedAt,
    required this.experienceReward,
    required this.requirement,
    required this.currentProgress,
    required this.targetProgress,
  });
  
  /// 从JSON创建Achievement对象
  factory Achievement.fromJson(Map<String, dynamic> json) {
    return Achievement(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      iconUrl: json['iconUrl'] as String,
      type: AchievementType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => AchievementType.exploration,
      ),
      rarity: AchievementRarity.values.firstWhere(
        (e) => e.name == json['rarity'],
        orElse: () => AchievementRarity.common,
      ),
      isCompleted: json['isCompleted'] as bool,
      completedAt: json['completedAt'] != null 
          ? DateTime.parse(json['completedAt'] as String)
          : null,
      experienceReward: json['experienceReward'] as int,
      requirement: json['requirement'] as String,
      currentProgress: json['currentProgress'] as int,
      targetProgress: json['targetProgress'] as int,
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'iconUrl': iconUrl,
      'type': type.name,
      'rarity': rarity.name,
      'isCompleted': isCompleted,
      'completedAt': completedAt?.toIso8601String(),
      'experienceReward': experienceReward,
      'requirement': requirement,
      'currentProgress': currentProgress,
      'targetProgress': targetProgress,
    };
  }
  
  /// 复制并修改部分属性
  Achievement copyWith({
    String? id,
    String? title,
    String? description,
    String? iconUrl,
    AchievementType? type,
    AchievementRarity? rarity,
    bool? isCompleted,
    DateTime? completedAt,
    int? experienceReward,
    String? requirement,
    int? currentProgress,
    int? targetProgress,
  }) {
    return Achievement(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      iconUrl: iconUrl ?? this.iconUrl,
      type: type ?? this.type,
      rarity: rarity ?? this.rarity,
      isCompleted: isCompleted ?? this.isCompleted,
      completedAt: completedAt ?? this.completedAt,
      experienceReward: experienceReward ?? this.experienceReward,
      requirement: requirement ?? this.requirement,
      currentProgress: currentProgress ?? this.currentProgress,
      targetProgress: targetProgress ?? this.targetProgress,
    );
  }
  
  /// 计算完成进度百分比
  double get progressPercentage {
    if (targetProgress <= 0) return 0.0;
    return (currentProgress / targetProgress).clamp(0.0, 1.0);
  }
  
  /// 是否接近完成（进度超过80%）
  bool get isNearCompletion {
    return progressPercentage >= 0.8;
  }
  
  @override
  String toString() {
    return 'Achievement(id: $id, title: $title, isCompleted: $isCompleted)';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Achievement && other.id == id;
  }
  
  @override
  int get hashCode => id.hashCode;
}

/// 成就类型枚举
enum AchievementType {
  /// 探索类成就
  exploration('exploration', '探索'),
  
  /// 收集类成就
  collection('collection', '收集'),
  
  /// 社交类成就
  social('social', '社交'),
  
  /// 挑战类成就
  challenge('challenge', '挑战'),
  
  /// 特殊类成就
  special('special', '特殊');
  
  const AchievementType(this.value, this.displayName);
  
  final String value;
  final String displayName;
}

/// 成就稀有度枚举
enum AchievementRarity {
  /// 普通
  common('common', '普通'),
  
  /// 稀有
  rare('rare', '稀有'),
  
  /// 史诗
  epic('epic', '史诗'),
  
  /// 传说
  legendary('legendary', '传说');
  
  const AchievementRarity(this.value, this.displayName);
  
  final String value;
  final String displayName;
}
