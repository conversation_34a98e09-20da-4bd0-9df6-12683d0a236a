/// 任务数据模型
/// 存储任务信息和状态
class Quest {
  /// 任务ID
  final String id;
  
  /// 任务标题
  final String title;
  
  /// 任务描述
  final String description;
  
  /// 任务类型
  final QuestType type;
  
  /// 任务状态
  final QuestStatus status;
  
  /// 目标位置纬度
  final double? targetLatitude;
  
  /// 目标位置经度
  final double? targetLongitude;
  
  /// 目标位置名称
  final String? targetLocationName;
  
  /// 完成半径（米）
  final double completionRadius;
  
  /// 经验值奖励
  final int experienceReward;
  
  /// 任务难度
  final QuestDifficulty difficulty;
  
  /// 预计完成时间（分钟）
  final int estimatedDuration;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 开始时间
  final DateTime? startedAt;
  
  /// 完成时间
  final DateTime? completedAt;
  
  /// 任务要求描述
  final String requirements;
  
  /// 构造函数
  const Quest({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.status,
    this.targetLatitude,
    this.targetLongitude,
    this.targetLocationName,
    required this.completionRadius,
    required this.experienceReward,
    required this.difficulty,
    required this.estimatedDuration,
    required this.createdAt,
    this.startedAt,
    this.completedAt,
    required this.requirements,
  });
  
  /// 从JSON创建Quest对象
  factory Quest.fromJson(Map<String, dynamic> json) {
    return Quest(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: QuestType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => QuestType.exploration,
      ),
      status: QuestStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => QuestStatus.available,
      ),
      targetLatitude: json['targetLatitude'] as double?,
      targetLongitude: json['targetLongitude'] as double?,
      targetLocationName: json['targetLocationName'] as String?,
      completionRadius: (json['completionRadius'] as num).toDouble(),
      experienceReward: json['experienceReward'] as int,
      difficulty: QuestDifficulty.values.firstWhere(
        (e) => e.name == json['difficulty'],
        orElse: () => QuestDifficulty.easy,
      ),
      estimatedDuration: json['estimatedDuration'] as int,
      createdAt: DateTime.parse(json['createdAt'] as String),
      startedAt: json['startedAt'] != null 
          ? DateTime.parse(json['startedAt'] as String)
          : null,
      completedAt: json['completedAt'] != null 
          ? DateTime.parse(json['completedAt'] as String)
          : null,
      requirements: json['requirements'] as String,
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.name,
      'status': status.name,
      'targetLatitude': targetLatitude,
      'targetLongitude': targetLongitude,
      'targetLocationName': targetLocationName,
      'completionRadius': completionRadius,
      'experienceReward': experienceReward,
      'difficulty': difficulty.name,
      'estimatedDuration': estimatedDuration,
      'createdAt': createdAt.toIso8601String(),
      'startedAt': startedAt?.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'requirements': requirements,
    };
  }
  
  /// 复制并修改部分属性
  Quest copyWith({
    String? id,
    String? title,
    String? description,
    QuestType? type,
    QuestStatus? status,
    double? targetLatitude,
    double? targetLongitude,
    String? targetLocationName,
    double? completionRadius,
    int? experienceReward,
    QuestDifficulty? difficulty,
    int? estimatedDuration,
    DateTime? createdAt,
    DateTime? startedAt,
    DateTime? completedAt,
    String? requirements,
  }) {
    return Quest(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      targetLatitude: targetLatitude ?? this.targetLatitude,
      targetLongitude: targetLongitude ?? this.targetLongitude,
      targetLocationName: targetLocationName ?? this.targetLocationName,
      completionRadius: completionRadius ?? this.completionRadius,
      experienceReward: experienceReward ?? this.experienceReward,
      difficulty: difficulty ?? this.difficulty,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      createdAt: createdAt ?? this.createdAt,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      requirements: requirements ?? this.requirements,
    );
  }
  
  /// 是否有目标位置
  bool get hasTargetLocation {
    return targetLatitude != null && targetLongitude != null;
  }
  
  /// 是否可以开始
  bool get canStart {
    return status == QuestStatus.available;
  }
  
  /// 是否进行中
  bool get isInProgress {
    return status == QuestStatus.inProgress;
  }
  
  /// 是否已完成
  bool get isCompleted {
    return status == QuestStatus.completed;
  }
  
  @override
  String toString() {
    return 'Quest(id: $id, title: $title, status: $status)';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Quest && other.id == id;
  }
  
  @override
  int get hashCode => id.hashCode;
}

/// 任务类型枚举
enum QuestType {
  /// 探索任务
  exploration('exploration', '探索'),
  
  /// 拍照任务
  photo('photo', '拍照'),
  
  /// 收集任务
  collection('collection', '收集'),
  
  /// 社交任务
  social('social', '社交'),
  
  /// 每日任务
  daily('daily', '每日');
  
  const QuestType(this.value, this.displayName);
  
  final String value;
  final String displayName;
}

/// 任务状态枚举
enum QuestStatus {
  /// 可接受
  available('available', '可接受'),
  
  /// 进行中
  inProgress('inProgress', '进行中'),
  
  /// 已完成
  completed('completed', '已完成'),
  
  /// 已过期
  expired('expired', '已过期');
  
  const QuestStatus(this.value, this.displayName);
  
  final String value;
  final String displayName;
}

/// 任务难度枚举
enum QuestDifficulty {
  /// 简单
  easy('easy', '简单'),
  
  /// 普通
  normal('normal', '普通'),
  
  /// 困难
  hard('hard', '困难'),
  
  /// 极难
  extreme('extreme', '极难');
  
  const QuestDifficulty(this.value, this.displayName);
  
  final String value;
  final String displayName;
}
