import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';
import '../constants/app_strings.dart';

/// 权限管理工具类
/// 统一处理应用所需的各种权限
class PermissionHelper {
  /// 检查并请求位置权限
  static Future<bool> requestLocationPermission() async {
    try {
      final status = await Permission.location.status;
      
      if (status.isGranted) {
        debugPrint('位置权限已授予');
        return true;
      }
      
      if (status.isDenied) {
        final result = await Permission.location.request();
        if (result.isGranted) {
          debugPrint('位置权限请求成功');
          return true;
        } else {
          debugPrint('位置权限被拒绝');
          return false;
        }
      }
      
      if (status.isPermanentlyDenied) {
        debugPrint('位置权限被永久拒绝，需要手动开启');
        return false;
      }
      
      return false;
    } catch (e) {
      debugPrint('请求位置权限时出错: $e');
      return false;
    }
  }
  
  /// 检查并请求相机权限
  static Future<bool> requestCameraPermission() async {
    try {
      final status = await Permission.camera.status;
      
      if (status.isGranted) {
        debugPrint('相机权限已授予');
        return true;
      }
      
      if (status.isDenied) {
        final result = await Permission.camera.request();
        if (result.isGranted) {
          debugPrint('相机权限请求成功');
          return true;
        } else {
          debugPrint('相机权限被拒绝');
          return false;
        }
      }
      
      if (status.isPermanentlyDenied) {
        debugPrint('相机权限被永久拒绝，需要手动开启');
        return false;
      }
      
      return false;
    } catch (e) {
      debugPrint('请求相机权限时出错: $e');
      return false;
    }
  }
  
  /// 检查并请求存储权限
  static Future<bool> requestStoragePermission() async {
    try {
      final status = await Permission.storage.status;
      
      if (status.isGranted) {
        debugPrint('存储权限已授予');
        return true;
      }
      
      if (status.isDenied) {
        final result = await Permission.storage.request();
        if (result.isGranted) {
          debugPrint('存储权限请求成功');
          return true;
        } else {
          debugPrint('存储权限被拒绝');
          return false;
        }
      }
      
      if (status.isPermanentlyDenied) {
        debugPrint('存储权限被永久拒绝，需要手动开启');
        return false;
      }
      
      return false;
    } catch (e) {
      debugPrint('请求存储权限时出错: $e');
      return false;
    }
  }
  
  /// 检查位置权限状态
  static Future<bool> isLocationPermissionGranted() async {
    try {
      final status = await Permission.location.status;
      return status.isGranted;
    } catch (e) {
      debugPrint('检查位置权限状态时出错: $e');
      return false;
    }
  }
  
  /// 检查相机权限状态
  static Future<bool> isCameraPermissionGranted() async {
    try {
      final status = await Permission.camera.status;
      return status.isGranted;
    } catch (e) {
      debugPrint('检查相机权限状态时出错: $e');
      return false;
    }
  }
  
  /// 打开应用设置页面
  static Future<void> openSettings() async {
    try {
      await openAppSettings();
      debugPrint('打开应用设置页面');
    } catch (e) {
      debugPrint('打开应用设置页面失败: $e');
    }
  }
  
  /// 请求所有必需权限
  static Future<Map<String, bool>> requestAllPermissions() async {
    final results = <String, bool>{};
    
    // 请求位置权限
    results['location'] = await requestLocationPermission();
    
    // 请求相机权限
    results['camera'] = await requestCameraPermission();
    
    // 请求存储权限
    results['storage'] = await requestStoragePermission();
    
    debugPrint('权限请求结果: $results');
    return results;
  }
  
  /// 检查是否所有权限都已授予
  static Future<bool> areAllPermissionsGranted() async {
    final locationGranted = await isLocationPermissionGranted();
    final cameraGranted = await isCameraPermissionGranted();
    
    return locationGranted && cameraGranted;
  }
  
  /// 获取权限状态描述
  static String getPermissionStatusDescription(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return '已授权';
      case PermissionStatus.denied:
        return '已拒绝';
      case PermissionStatus.restricted:
        return '受限制';
      case PermissionStatus.limited:
        return '有限授权';
      case PermissionStatus.permanentlyDenied:
        return '永久拒绝';
      default:
        return '未知状态';
    }
  }
  
  // 私有构造函数，防止实例化
  PermissionHelper._();
}
