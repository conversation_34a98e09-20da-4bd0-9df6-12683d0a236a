import 'package:flutter/foundation.dart';
import '../models/achievement.dart';

/// 成就数据管理Provider
/// 管理成就列表、进度和状态
class AchievementProvider extends ChangeNotifier {
  // 私有字段
  List<Achievement> _achievements = [];
  bool _isLoading = false;
  String? _errorMessage;
  
  // 公共getter
  /// 所有成就列表
  List<Achievement> get allAchievements => List.unmodifiable(_achievements);
  
  /// 已完成的成就列表
  List<Achievement> get completedAchievements {
    return _achievements.where((achievement) => achievement.isCompleted).toList();
  }
  
  /// 进行中的成就列表
  List<Achievement> get inProgressAchievements {
    return _achievements.where((achievement) => 
        !achievement.isCompleted && achievement.currentProgress > 0).toList();
  }
  
  /// 未开始的成就列表
  List<Achievement> get notStartedAchievements {
    return _achievements.where((achievement) => 
        !achievement.isCompleted && achievement.currentProgress == 0).toList();
  }
  
  /// 是否正在加载
  bool get isLoading => _isLoading;
  
  /// 错误信息
  String? get errorMessage => _errorMessage;
  
  /// 完成百分比
  double get completionPercentage {
    if (_achievements.isEmpty) return 0.0;
    return (completedAchievements.length / _achievements.length) * 100;
  }
  
  /// 总经验奖励
  int get totalExperienceReward {
    return completedAchievements.fold(0, (sum, achievement) => 
        sum + achievement.experienceReward);
  }
  
  /// 初始化成就数据
  Future<void> initializeAchievements() async {
    try {
      _setLoading(true);
      _clearError();
      
      // 模拟加载成就数据
      await Future.delayed(const Duration(milliseconds: 500));
      
      // 生成示例成就数据
      _generateSampleAchievements();
      
      debugPrint('成就数据初始化完成');
      debugPrint('总成就数: ${_achievements.length}');
      debugPrint('已完成: ${completedAchievements.length}');
      debugPrint('进行中: ${inProgressAchievements.length}');
    } catch (e) {
      _setError('加载成就数据失败: $e');
      debugPrint('成就数据初始化错误: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// 更新成就进度
  Future<void> updateAchievementProgress(String achievementId, int progress) async {
    try {
      final index = _achievements.indexWhere((a) => a.id == achievementId);
      if (index == -1) {
        throw Exception('成就不存在');
      }
      
      final achievement = _achievements[index];
      if (achievement.isCompleted) {
        return; // 已完成的成就不需要更新
      }
      
      final newProgress = progress.clamp(0, achievement.targetProgress);
      final isCompleted = newProgress >= achievement.targetProgress;
      
      final updatedAchievement = achievement.copyWith(
        currentProgress: newProgress,
        isCompleted: isCompleted,
        completedAt: isCompleted ? DateTime.now() : null,
      );
      
      _achievements[index] = updatedAchievement;
      
      if (isCompleted) {
        debugPrint('成就完成: ${achievement.title}，获得经验: ${achievement.experienceReward}');
      } else {
        debugPrint('成就进度更新: ${achievement.title} ($newProgress/${achievement.targetProgress})');
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('更新成就进度失败: $e');
    }
  }
  
  /// 完成成就
  Future<void> completeAchievement(String achievementId) async {
    try {
      final index = _achievements.indexWhere((a) => a.id == achievementId);
      if (index == -1) {
        throw Exception('成就不存在');
      }
      
      final achievement = _achievements[index];
      if (achievement.isCompleted) {
        return; // 已完成的成就
      }
      
      final updatedAchievement = achievement.copyWith(
        currentProgress: achievement.targetProgress,
        isCompleted: true,
        completedAt: DateTime.now(),
      );
      
      _achievements[index] = updatedAchievement;
      
      debugPrint('成就完成: ${achievement.title}，获得经验: ${achievement.experienceReward}');
      notifyListeners();
    } catch (e) {
      debugPrint('完成成就失败: $e');
    }
  }
  
  /// 根据类型获取成就
  List<Achievement> getAchievementsByType(AchievementType type) {
    return _achievements.where((achievement) => achievement.type == type).toList();
  }
  
  /// 根据稀有度获取成就
  List<Achievement> getAchievementsByRarity(AchievementRarity rarity) {
    return _achievements.where((achievement) => achievement.rarity == rarity).toList();
  }
  
  /// 获取接近完成的成就
  List<Achievement> getNearCompletionAchievements() {
    return _achievements.where((achievement) => 
        !achievement.isCompleted && achievement.isNearCompletion).toList();
  }
  
  /// 刷新成就数据
  Future<void> refreshAchievements() async {
    await initializeAchievements();
  }
  
  /// 清理数据
  void clear() {
    _achievements.clear();
    _isLoading = false;
    _errorMessage = null;
    notifyListeners();
  }
  
  // 私有方法
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }
  
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }
  
  void _clearError() {
    if (_errorMessage != null) {
      _errorMessage = null;
      notifyListeners();
    }
  }
  
  /// 生成示例成就数据
  void _generateSampleAchievements() {
    final now = DateTime.now();
    
    _achievements = [
      // 探索类成就
      Achievement(
        id: 'achievement_1',
        title: '初次探险',
        description: '完成第一个探索任务',
        iconUrl: 'assets/icons/exploration.png',
        type: AchievementType.exploration,
        rarity: AchievementRarity.common,
        isCompleted: true,
        completedAt: now.subtract(const Duration(hours: 2)),
        experienceReward: 50,
        requirement: '完成1个探索任务',
        currentProgress: 1,
        targetProgress: 1,
      ),
      
      Achievement(
        id: 'achievement_2',
        title: '探险家',
        description: '完成10个探索任务',
        iconUrl: 'assets/icons/explorer.png',
        type: AchievementType.exploration,
        rarity: AchievementRarity.rare,
        isCompleted: false,
        experienceReward: 200,
        requirement: '完成10个探索任务',
        currentProgress: 3,
        targetProgress: 10,
      ),
      
      // 收集类成就
      Achievement(
        id: 'achievement_3',
        title: '收藏家',
        description: '收集5个不同类型的物品',
        iconUrl: 'assets/icons/collection.png',
        type: AchievementType.collection,
        rarity: AchievementRarity.common,
        isCompleted: false,
        experienceReward: 100,
        requirement: '收集5个不同物品',
        currentProgress: 2,
        targetProgress: 5,
      ),
      
      Achievement(
        id: 'achievement_4',
        title: '宝藏猎人',
        description: '发现20个隐藏宝藏',
        iconUrl: 'assets/icons/treasure.png',
        type: AchievementType.collection,
        rarity: AchievementRarity.epic,
        isCompleted: false,
        experienceReward: 500,
        requirement: '发现20个隐藏宝藏',
        currentProgress: 0,
        targetProgress: 20,
      ),
      
      // 社交类成就
      Achievement(
        id: 'achievement_5',
        title: '社交达人',
        description: '与其他玩家互动10次',
        iconUrl: 'assets/icons/social.png',
        type: AchievementType.social,
        rarity: AchievementRarity.rare,
        isCompleted: false,
        experienceReward: 150,
        requirement: '与其他玩家互动10次',
        currentProgress: 0,
        targetProgress: 10,
      ),
      
      // 挑战类成就
      Achievement(
        id: 'achievement_6',
        title: '挑战者',
        description: '完成一个困难任务',
        iconUrl: 'assets/icons/challenge.png',
        type: AchievementType.challenge,
        rarity: AchievementRarity.epic,
        isCompleted: false,
        experienceReward: 300,
        requirement: '完成1个困难任务',
        currentProgress: 0,
        targetProgress: 1,
      ),
      
      // 特殊成就
      Achievement(
        id: 'achievement_7',
        title: '传说探险家',
        description: '达到10级并完成所有基础成就',
        iconUrl: 'assets/icons/legendary.png',
        type: AchievementType.special,
        rarity: AchievementRarity.legendary,
        isCompleted: false,
        experienceReward: 1000,
        requirement: '达到10级并完成所有基础成就',
        currentProgress: 0,
        targetProgress: 1,
      ),
    ];
    
    notifyListeners();
  }
  
  @override
  void dispose() {
    clear();
    super.dispose();
  }
}
