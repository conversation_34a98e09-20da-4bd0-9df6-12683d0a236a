import 'package:flutter/material.dart';

/// 宝藏奇缘颜色主题定义
/// 二次元可爱风格配色方案
class AppColors {
  // === 主色调 ===
  /// 主要金色 - 宝石币、重要按钮
  static const Color primaryGold = Color(0xFFFFD700);

  /// 主要橙色 - 活力按钮、强调色
  static const Color primaryOrange = Color(0xFFFF8C00);

  /// 神秘紫色 - 稀有物品、特殊功能
  static const Color mysteryPurple = Color(0xFF8A2BE2);

  /// 深紫色 - 传说物品、高级功能
  static const Color deepPurple = Color(0xFF9370DB);

  // === 辅助色 ===
  /// 天空蓝 - 清新、信息提示
  static const Color skyBlue = Color(0xFF87CEEB);

  /// 活力绿 - 成功、完成状态
  static const Color limeGreen = Color(0xFF32CD32);

  /// 温柔粉 - 可爱元素、女性化设计
  static const Color softPink = Color(0xFFFFB6C1);

  /// 卡片白 - 卡片背景、内容区域
  static const Color cardWhite = Color(0xFFFFFFF8);

  // === 背景渐变 ===
  /// 主背景渐变 - 温暖梦幻感
  static const LinearGradient backgroundGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [Color(0xFFFFE4B5), Color(0xFFE6E6FA)],
  );

  // === 功能性颜色 ===
  /// 成功绿色
  static const Color successGreen = Color(0xFF4CAF50);

  /// 警告橙色
  static const Color warningOrange = Color(0xFFFF9800);

  /// 错误红色
  static const Color errorRed = Color(0xFFF44336);

  /// 信息蓝色
  static const Color infoBlue = Color(0xFF2196F3);

  // === 文本颜色 ===
  /// 主要文本 - 深灰色
  static const Color textPrimary = Color(0xFF333333);

  /// 次要文本 - 中灰色
  static const Color textSecondary = Color(0xFF666666);

  /// 提示文本 - 浅灰色
  static const Color textHint = Color(0xFF999999);

  /// 禁用文本 - 更浅灰色
  static const Color textDisabled = Color(0xFFCCCCCC);

  // === 边框和分割线 ===
  /// 边框颜色
  static const Color border = Color(0xFFE0E0E0);

  /// 分割线颜色
  static const Color divider = Color(0xFFF0F0F0);

  // === 稀有度颜色 ===
  /// 普通卡片 - 灰色
  static const Color rarityCommon = Color(0xFF9E9E9E);

  /// 稀有卡片 - 蓝色
  static const Color rarityRare = Color(0xFF2196F3);

  /// 传说卡片 - 紫色
  static const Color rarityLegendary = Color(0xFF9C27B0);

  /// 限定卡片 - 金色
  static const Color rarityLimited = Color(0xFFFFD700);

  // === 渐变定义 ===
  /// 金色渐变
  static const LinearGradient goldGradient = LinearGradient(
    colors: [primaryGold, primaryOrange],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// 紫色渐变
  static const LinearGradient purpleGradient = LinearGradient(
    colors: [mysteryPurple, deepPurple],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// 蓝色渐变
  static const LinearGradient blueGradient = LinearGradient(
    colors: [skyBlue, infoBlue],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// 粉色渐变
  static const LinearGradient pinkGradient = LinearGradient(
    colors: [softPink, Color(0xFFFF69B4)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // === 兼容性别名（保持向后兼容）===
  static const Color treasureGold = primaryGold;
  static const Color backgroundWhite = cardWhite;
  static const Color textBlack = textPrimary;
  static const Color borderGray = border;
  static const Color warningRed = errorRed;
  static const Color emeraldGreen = limeGreen;

  // 私有构造函数，防止实例化
  AppColors._();
}
