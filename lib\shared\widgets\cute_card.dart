import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';

/// 可爱风格卡片组件
/// 符合二次元设计风格
class CuteCard extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final double? width;
  final double? height;
  final bool showShadow;
  final double borderRadius;
  final Color? borderColor;
  final double borderWidth;

  const CuteCard({
    super.key,
    required this.child,
    this.onTap,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.width,
    this.height,
    this.showShadow = true,
    this.borderRadius = 16.0,
    this.borderColor,
    this.borderWidth = 0,
  });

  @override
  State<CuteCard> createState() => _CuteCardState();
}

class _CuteCardState extends State<CuteCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            width: widget.width,
            height: widget.height,
            margin: widget.margin ?? const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: widget.backgroundColor ?? AppColors.cardWhite,
              borderRadius: BorderRadius.circular(widget.borderRadius),
              border: widget.borderColor != null
                  ? Border.all(
                      color: widget.borderColor!,
                      width: widget.borderWidth,
                    )
                  : null,
              boxShadow: widget.showShadow
                  ? [
                      BoxShadow(
                        color: AppColors.primaryGold.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ]
                  : null,
            ),
            child: Material(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(widget.borderRadius),
              child: InkWell(
                onTap: widget.onTap,
                onTapDown: widget.onTap != null
                    ? (_) => _animationController.forward()
                    : null,
                onTapUp: widget.onTap != null
                    ? (_) => _animationController.reverse()
                    : null,
                onTapCancel: widget.onTap != null
                    ? () => _animationController.reverse()
                    : null,
                borderRadius: BorderRadius.circular(widget.borderRadius),
                child: Container(
                  padding: widget.padding ?? const EdgeInsets.all(16.0),
                  child: widget.child,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// 特殊风格的可爱卡片
class SpecialCuteCard extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final CuteCardType type;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;

  const SpecialCuteCard({
    super.key,
    required this.child,
    this.onTap,
    this.type = CuteCardType.normal,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return CuteCard(
      onTap: onTap,
      padding: padding,
      margin: margin,
      backgroundColor: _getBackgroundColor(),
      borderColor: _getBorderColor(),
      borderWidth: 2,
      child: Container(
        decoration: BoxDecoration(
          gradient: _getGradient(),
          borderRadius: BorderRadius.circular(14),
        ),
        child: Container(
          padding: const EdgeInsets.all(2),
          child: child,
        ),
      ),
    );
  }

  Color _getBackgroundColor() {
    switch (type) {
      case CuteCardType.gold:
        return AppColors.primaryGold.withOpacity(0.1);
      case CuteCardType.purple:
        return AppColors.mysteryPurple.withOpacity(0.1);
      case CuteCardType.blue:
        return AppColors.skyBlue.withOpacity(0.1);
      case CuteCardType.pink:
        return AppColors.softPink.withOpacity(0.1);
      case CuteCardType.normal:
      default:
        return AppColors.cardWhite;
    }
  }

  Color _getBorderColor() {
    switch (type) {
      case CuteCardType.gold:
        return AppColors.primaryGold;
      case CuteCardType.purple:
        return AppColors.mysteryPurple;
      case CuteCardType.blue:
        return AppColors.skyBlue;
      case CuteCardType.pink:
        return AppColors.softPink;
      case CuteCardType.normal:
      default:
        return AppColors.border;
    }
  }

  LinearGradient? _getGradient() {
    switch (type) {
      case CuteCardType.gold:
        return AppColors.goldGradient;
      case CuteCardType.purple:
        return AppColors.purpleGradient;
      case CuteCardType.blue:
        return AppColors.blueGradient;
      case CuteCardType.pink:
        return AppColors.pinkGradient;
      case CuteCardType.normal:
      default:
        return null;
    }
  }
}

/// 可爱卡片类型枚举
enum CuteCardType {
  /// 普通卡片
  normal,
  
  /// 金色卡片 - 用于重要内容
  gold,
  
  /// 紫色卡片 - 用于稀有内容
  purple,
  
  /// 蓝色卡片 - 用于信息提示
  blue,
  
  /// 粉色卡片 - 用于可爱元素
  pink,
}

/// 成就卡片组件
class AchievementCard extends StatelessWidget {
  final String title;
  final String description;
  final String imageUrl;
  final String rarity;
  final bool isOwned;
  final VoidCallback? onTap;

  const AchievementCard({
    super.key,
    required this.title,
    required this.description,
    required this.imageUrl,
    required this.rarity,
    this.isOwned = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return SpecialCuteCard(
      type: _getRarityType(),
      onTap: onTap,
      child: Column(
        children: [
          // 卡片图片
          Container(
            width: double.infinity,
            height: 120,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              image: DecorationImage(
                image: NetworkImage(imageUrl),
                fit: BoxFit.cover,
              ),
            ),
            child: isOwned
                ? null
                : Container(
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.lock,
                        color: Colors.white,
                        size: 32,
                      ),
                    ),
                  ),
          ),
          
          const SizedBox(height: 8),
          
          // 卡片信息
          Text(
            title,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          
          const SizedBox(height: 4),
          
          Text(
            description,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          
          const SizedBox(height: 8),
          
          // 稀有度标签
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getRarityColor(),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              rarity,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  CuteCardType _getRarityType() {
    switch (rarity.toLowerCase()) {
      case '稀有':
        return CuteCardType.blue;
      case '传说':
        return CuteCardType.purple;
      case '限定':
        return CuteCardType.gold;
      default:
        return CuteCardType.normal;
    }
  }

  Color _getRarityColor() {
    switch (rarity.toLowerCase()) {
      case '稀有':
        return AppColors.rarityRare;
      case '传说':
        return AppColors.rarityLegendary;
      case '限定':
        return AppColors.rarityLimited;
      default:
        return AppColors.rarityCommon;
    }
  }
}
