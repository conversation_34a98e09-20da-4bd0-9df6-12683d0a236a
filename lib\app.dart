import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'core/constants/app_strings.dart';
import 'core/constants/app_colors.dart';
import 'core/theme/app_theme.dart';
import 'shared/providers/location_provider.dart';
import 'app/main_app.dart';

/// 宝藏奇缘应用主类
/// 配置应用的主题、路由和状态管理
class TreasureOdysseyApp extends StatelessWidget {
  const TreasureOdysseyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => LocationProvider()),
        // TODO: 添加其他Provider
      ],
      child: MaterialApp(
        title: AppStrings.appName,
        theme: AppTheme.themeData,
        home: const AppWrapper(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}

/// 应用包装器 - 提供背景渐变
class AppWrapper extends StatelessWidget {
  const AppWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: AppColors.backgroundGradient,
      ),
      child: const MainApp(),
    );
  }
}
