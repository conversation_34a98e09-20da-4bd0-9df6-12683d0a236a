import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'core/constants/app_strings.dart';
import 'core/theme/app_theme.dart';
import 'providers/game_provider.dart';
import 'providers/player_provider.dart';
import 'providers/quest_provider.dart';
import 'providers/achievement_provider.dart';
import 'screens/home/<USER>';

/// 应用根组件
/// 配置主题、Provider和路由
class TreasureOdysseyApp extends StatelessWidget {
  const TreasureOdysseyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        /// 游戏状态管理
        ChangeNotifierProvider(create: (_) => GameProvider()),

        /// 玩家数据管理
        ChangeNotifierProvider(create: (_) => PlayerProvider()),

        /// 任务数据管理
        ChangeNotifierProvider(create: (_) => QuestProvider()),

        /// 成就数据管理
        ChangeNotifierProvider(create: (_) => AchievementProvider()),
      ],
      child: MaterialApp(
        title: AppStrings.appName,
        theme: AppTheme.themeData,
        home: const HomeScreen(),
        debugShowCheckedModeBanner: false,
        
        // 路由配置
        routes: {
          '/home': (context) => const HomeScreen(),
          // 其他路由将在后续添加
        },
        
        // 初始路由
        initialRoute: '/home',
        
        // 路由生成器（用于动态路由）
        onGenerateRoute: (settings) {
          switch (settings.name) {
            case '/home':
              return MaterialPageRoute(
                builder: (context) => const HomeScreen(),
              );
            default:
              return MaterialPageRoute(
                builder: (context) => const HomeScreen(),
              );
          }
        },
        
        // 未知路由处理
        onUnknownRoute: (settings) {
          return MaterialPageRoute(
            builder: (context) => const HomeScreen(),
          );
        },
      ),
    );
  }
}
