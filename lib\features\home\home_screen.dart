import 'package:flutter/material.dart';
import 'package:animate_do/animate_do.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_strings.dart';
import '../../core/constants/app_sizes.dart';
import '../../shared/widgets/cute_card.dart';
import '../../shared/widgets/cute_button.dart';

/// 游戏首页 - 宝藏奇缘欢迎界面
/// 展示游戏世界观和引导用户开始探险
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _floatingController;
  late AnimationController _sparkleController;

  @override
  void initState() {
    super.initState();
    _floatingController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);
    
    _sparkleController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _floatingController.dispose();
    _sparkleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: AppColors.backgroundGradient,
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppSizes.spacing20),
            child: Column(
              children: [
                const SizedBox(height: AppSizes.spacing20),
                
                // 游戏标题和Logo
                _buildGameHeader(),
                
                const SizedBox(height: AppSizes.spacing32),
                
                // 主要功能卡片
                _buildMainFeatures(),
                
                const SizedBox(height: AppSizes.spacing24),
                
                // 每日任务预览
                _buildDailyTasks(),
                
                const SizedBox(height: AppSizes.spacing24),
                
                // 探险统计
                _buildAdventureStats(),
                
                const SizedBox(height: AppSizes.spacing32),
                
                // 开始探险按钮
                _buildStartAdventureButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建游戏头部
  Widget _buildGameHeader() {
    return FadeInDown(
      duration: const Duration(milliseconds: 800),
      child: Column(
        children: [
          // 游戏Logo
          AnimatedBuilder(
            animation: _floatingController,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(0, _floatingController.value * 10 - 5),
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    gradient: AppColors.goldGradient,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primaryGold.withOpacity(0.4),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.diamond,
                    size: 60,
                    color: Colors.white,
                  ),
                ),
              );
            },
          ),
          
          const SizedBox(height: AppSizes.spacing16),
          
          // 游戏标题
          ShaderMask(
            shaderCallback: (bounds) => AppColors.goldGradient.createShader(bounds),
            child: Text(
              '宝藏奇缘',
              style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          
          const SizedBox(height: AppSizes.spacing8),
          
          Text(
            '现实秘境探险游戏',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建主要功能
  Widget _buildMainFeatures() {
    return FadeInUp(
      duration: const Duration(milliseconds: 800),
      delay: const Duration(milliseconds: 200),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '🎮 探险功能',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: AppSizes.spacing16),
          
          Row(
            children: [
              Expanded(
                child: _buildFeatureCard(
                  '🗺️',
                  '探险地图',
                  '发现附近的神秘宝藏',
                  AppColors.blueGradient,
                ),
              ),
              const SizedBox(width: AppSizes.spacing12),
              Expanded(
                child: _buildFeatureCard(
                  '🎒',
                  '背包仓库',
                  '管理你的珍贵收藏',
                  AppColors.purpleGradient,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppSizes.spacing12),
          
          Row(
            children: [
              Expanded(
                child: _buildFeatureCard(
                  '🏆',
                  '成就收藏',
                  'NFT成就卡片展示',
                  AppColors.goldGradient,
                ),
              ),
              const SizedBox(width: AppSizes.spacing12),
              Expanded(
                child: _buildFeatureCard(
                  '🛒',
                  '交易市场',
                  '与其他探险家交易',
                  AppColors.pinkGradient,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建功能卡片
  Widget _buildFeatureCard(
    String emoji,
    String title,
    String subtitle,
    LinearGradient gradient,
  ) {
    return CuteCard(
      onTap: () {
        // TODO: 导航到对应功能
      },
      child: Column(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              gradient: gradient,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                emoji,
                style: const TextStyle(fontSize: 24),
              ),
            ),
          ),
          const SizedBox(height: AppSizes.spacing8),
          
          Text(
            title,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSizes.spacing4),
          
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// 构建每日任务
  Widget _buildDailyTasks() {
    return FadeInLeft(
      duration: const Duration(milliseconds: 800),
      delay: const Duration(milliseconds: 400),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '📅 今日任务',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSizes.spacing8,
                  vertical: AppSizes.spacing4,
                ),
                decoration: BoxDecoration(
                  gradient: AppColors.goldGradient,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  '2/3 完成',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSizes.spacing16),
          
          CuteCard(
            child: Column(
              children: [
                _buildTaskProgress('☕ 咖啡店签到', true, '20'),
                const Divider(height: 1),
                _buildTaskProgress('📸 风景拍照', true, '30'),
                const Divider(height: 1),
                _buildTaskProgress('🛍️ 商店消费', false, '50'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建任务进度
  Widget _buildTaskProgress(String title, bool completed, String reward) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSizes.spacing16,
        vertical: AppSizes.spacing12,
      ),
      child: Row(
        children: [
          Icon(
            completed ? Icons.check_circle : Icons.radio_button_unchecked,
            color: completed ? AppColors.limeGreen : AppColors.textHint,
            size: 20,
          ),
          const SizedBox(width: AppSizes.spacing12),
          
          Expanded(
            child: Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: completed ? AppColors.textSecondary : AppColors.textPrimary,
                decoration: completed ? TextDecoration.lineThrough : null,
              ),
            ),
          ),
          
          Text(
            '+$reward 💎',
            style: TextStyle(
              color: completed ? AppColors.textHint : AppColors.primaryGold,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建探险统计
  Widget _buildAdventureStats() {
    return FadeInRight(
      duration: const Duration(milliseconds: 800),
      delay: const Duration(milliseconds: 600),
      child: CuteCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '📊 探险统计',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSizes.spacing16),
            
            Row(
              children: [
                Expanded(child: _buildStatItem('💎', '1,234', '宝石币')),
                Expanded(child: _buildStatItem('🏆', '15', '成就数')),
                Expanded(child: _buildStatItem('📍', '42', '探索点')),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建统计项
  Widget _buildStatItem(String emoji, String value, String label) {
    return Column(
      children: [
        Text(emoji, style: const TextStyle(fontSize: 24)),
        const SizedBox(height: AppSizes.spacing4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primaryGold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  /// 构建开始探险按钮
  Widget _buildStartAdventureButton() {
    return FadeInUp(
      duration: const Duration(milliseconds: 800),
      delay: const Duration(milliseconds: 800),
      child: AnimatedBuilder(
        animation: _sparkleController,
        builder: (context, child) {
          return Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primaryGold.withOpacity(0.3 + _sparkleController.value * 0.2),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: CuteButton(
              text: '🚀 开始今日探险',
              onPressed: () {
                // TODO: 导航到地图页面
              },
              style: CuteButtonStyle.primary,
            ),
          );
        },
      ),
    );
  }
}
