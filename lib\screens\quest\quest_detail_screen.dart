import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/constants/app_colors.dart';
import '../../core/constants/app_strings.dart';
import '../../core/constants/app_sizes.dart';
import '../../core/services/camera_service.dart';
import '../../core/services/location_service.dart';
import '../../providers/quest_provider.dart';
import '../../providers/player_provider.dart';
import '../../models/quest.dart';

/// 任务详情页面 - 显示任务详细信息和执行操作
class QuestDetailScreen extends StatefulWidget {
  final Quest quest;
  
  const QuestDetailScreen({
    super.key,
    required this.quest,
  });

  @override
  State<QuestDetailScreen> createState() => _QuestDetailScreenState();
}

class _QuestDetailScreenState extends State<QuestDetailScreen> {
  final CameraService _cameraService = CameraService.instance;
  final LocationService _locationService = LocationService.instance;
  
  bool _isLoading = false;
  File? _capturedImage;
  bool _isInRange = false;
  
  @override
  void initState() {
    super.initState();
    _checkLocationRange();
  }

  /// 检查是否在任务范围内
  void _checkLocationRange() {
    if (widget.quest.hasTargetLocation) {
      final isInRange = _locationService.isWithinRange(
        widget.quest.targetLatitude!,
        widget.quest.targetLongitude!,
        widget.quest.completionRadius,
      );
      setState(() {
        _isInRange = isInRange;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.quest.title),
        backgroundColor: AppColors.mysteryBlue,
        actions: [
          if (widget.quest.isInProgress)
            IconButton(
              icon: const Icon(Icons.check),
              onPressed: _canCompleteQuest() ? _completeQuest : null,
              tooltip: '完成任务',
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSizes.spacing16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 任务状态卡片
            _buildStatusCard(),
            
            const SizedBox(height: AppSizes.spacing16),
            
            // 任务信息卡片
            _buildQuestInfoCard(),
            
            const SizedBox(height: AppSizes.spacing16),
            
            // 位置信息卡片
            if (widget.quest.hasTargetLocation) ...[
              _buildLocationCard(),
              const SizedBox(height: AppSizes.spacing16),
            ],
            
            // 任务要求卡片
            _buildRequirementsCard(),
            
            const SizedBox(height: AppSizes.spacing16),
            
            // 拍照区域
            if (widget.quest.type == QuestType.photo) ...[
              _buildPhotoSection(),
              const SizedBox(height: AppSizes.spacing16),
            ],
            
            // 操作按钮
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  /// 构建状态卡片
  Widget _buildStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.cardPadding),
        child: Row(
          children: [
            Icon(
              _getStatusIcon(),
              size: AppSizes.iconLarge,
              color: _getStatusColor(),
            ),
            const SizedBox(width: AppSizes.spacing16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.quest.status.displayName,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: _getStatusColor(),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (widget.quest.hasTargetLocation) ...[
                    const SizedBox(height: AppSizes.spacing4),
                    Row(
                      children: [
                        Icon(
                          _isInRange ? Icons.location_on : Icons.location_off,
                          size: AppSizes.iconSmall,
                          color: _isInRange ? AppColors.emeraldGreen : AppColors.warningRed,
                        ),
                        const SizedBox(width: AppSizes.spacing4),
                        Text(
                          _isInRange ? '在任务范围内' : '不在任务范围内',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: _isInRange ? AppColors.emeraldGreen : AppColors.warningRed,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建任务信息卡片
  Widget _buildQuestInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.cardPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '任务信息',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: AppSizes.spacing16),
            
            Text(
              widget.quest.description,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: AppSizes.spacing16),
            
            Row(
              children: [
                _buildInfoChip('类型', widget.quest.type.displayName),
                const SizedBox(width: AppSizes.spacing8),
                _buildInfoChip('难度', widget.quest.difficulty.displayName),
                const SizedBox(width: AppSizes.spacing8),
                _buildInfoChip('奖励', '${widget.quest.experienceReward} EXP'),
              ],
            ),
            const SizedBox(height: AppSizes.spacing8),
            
            Row(
              children: [
                _buildInfoChip('预计时间', '${widget.quest.estimatedDuration} 分钟'),
                if (widget.quest.hasTargetLocation) ...[
                  const SizedBox(width: AppSizes.spacing8),
                  _buildInfoChip('完成范围', '${widget.quest.completionRadius.toInt()} 米'),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建位置信息卡片
  Widget _buildLocationCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.cardPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '目标位置',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: AppSizes.spacing16),
            
            if (widget.quest.targetLocationName != null) ...[
              Row(
                children: [
                  const Icon(Icons.place, color: AppColors.mysteryBlue),
                  const SizedBox(width: AppSizes.spacing8),
                  Expanded(
                    child: Text(
                      widget.quest.targetLocationName!,
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppSizes.spacing8),
            ],
            
            Row(
              children: [
                const Icon(Icons.gps_fixed, color: AppColors.mysteryBlue),
                const SizedBox(width: AppSizes.spacing8),
                Expanded(
                  child: Text(
                    LocationService.formatCoordinates(
                      widget.quest.targetLatitude!,
                      widget.quest.targetLongitude!,
                    ),
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppSizes.spacing16),
            
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _openInMaps,
                icon: const Icon(Icons.map),
                label: const Text('在地图中查看'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建任务要求卡片
  Widget _buildRequirementsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.cardPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '完成要求',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: AppSizes.spacing16),
            
            Text(
              widget.quest.requirements,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建拍照区域
  Widget _buildPhotoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.cardPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '任务照片',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: AppSizes.spacing16),
            
            if (_capturedImage != null) ...[
              ClipRRect(
                borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                child: Image.file(
                  _capturedImage!,
                  width: double.infinity,
                  height: 200,
                  fit: BoxFit.cover,
                ),
              ),
              const SizedBox(height: AppSizes.spacing16),
              
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _retakePhoto,
                      icon: const Icon(Icons.camera_alt),
                      label: const Text('重新拍照'),
                    ),
                  ),
                  const SizedBox(width: AppSizes.spacing8),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _submitPhoto,
                      icon: const Icon(Icons.check),
                      label: const Text('提交照片'),
                    ),
                  ),
                ],
              ),
            ] else ...[
              Container(
                width: double.infinity,
                height: 200,
                decoration: BoxDecoration(
                  color: AppColors.backgroundWhite,
                  border: Border.all(color: AppColors.borderGray, width: 2),
                  borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.camera_alt,
                      size: AppSizes.iconXLarge,
                      color: AppColors.borderGray,
                    ),
                    const SizedBox(height: AppSizes.spacing8),
                    Text(
                      '点击拍照完成任务',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.borderGray,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: AppSizes.spacing16),
              
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _takePhoto,
                  icon: const Icon(Icons.camera_alt),
                  label: const Text('拍照'),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    if (widget.quest.canStart) {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: _isLoading ? null : _startQuest,
          child: _isLoading
              ? const CircularProgressIndicator(color: Colors.white)
              : const Text('开始任务'),
        ),
      );
    } else if (widget.quest.isInProgress) {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: _canCompleteQuest() && !_isLoading ? _completeQuest : null,
          child: _isLoading
              ? const CircularProgressIndicator(color: Colors.white)
              : const Text('完成任务'),
        ),
      );
    } else {
      return const SizedBox.shrink();
    }
  }

  /// 构建信息标签
  Widget _buildInfoChip(String label, String value) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSizes.spacing8,
        vertical: AppSizes.spacing4,
      ),
      decoration: BoxDecoration(
        color: AppColors.backgroundWhite,
        border: Border.all(color: AppColors.borderGray),
        borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
      ),
      child: Text(
        '$label: $value',
        style: Theme.of(context).textTheme.bodySmall,
      ),
    );
  }

  /// 获取状态图标
  IconData _getStatusIcon() {
    switch (widget.quest.status) {
      case QuestStatus.available:
        return Icons.play_circle_outline;
      case QuestStatus.inProgress:
        return Icons.pending;
      case QuestStatus.completed:
        return Icons.check_circle;
      case QuestStatus.expired:
        return Icons.cancel;
    }
  }

  /// 获取状态颜色
  Color _getStatusColor() {
    switch (widget.quest.status) {
      case QuestStatus.available:
        return AppColors.emeraldGreen;
      case QuestStatus.inProgress:
        return AppColors.treasureGold;
      case QuestStatus.completed:
        return AppColors.emeraldGreen;
      case QuestStatus.expired:
        return AppColors.warningRed;
    }
  }

  /// 检查是否可以完成任务
  bool _canCompleteQuest() {
    if (!widget.quest.isInProgress) return false;
    
    // 如果是拍照任务，需要有照片
    if (widget.quest.type == QuestType.photo && _capturedImage == null) {
      return false;
    }
    
    // 如果有位置要求，需要在范围内
    if (widget.quest.hasTargetLocation && !_isInRange) {
      return false;
    }
    
    return true;
  }

  /// 开始任务
  Future<void> _startQuest() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final questProvider = context.read<QuestProvider>();
      await questProvider.startQuest(widget.quest.id);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已开始任务: ${widget.quest.title}'),
            backgroundColor: AppColors.emeraldGreen,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('开始任务失败: $e'),
            backgroundColor: AppColors.warningRed,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 完成任务
  Future<void> _completeQuest() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final questProvider = context.read<QuestProvider>();
      final playerProvider = context.read<PlayerProvider>();
      
      await questProvider.completeQuest(widget.quest.id);
      await playerProvider.addExperience(widget.quest.experienceReward);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('任务完成！获得 ${widget.quest.experienceReward} 经验值'),
            backgroundColor: AppColors.emeraldGreen,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('完成任务失败: $e'),
            backgroundColor: AppColors.warningRed,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 拍照
  Future<void> _takePhoto() async {
    final image = await _cameraService.takePicture();
    if (image != null) {
      setState(() {
        _capturedImage = image;
      });
    }
  }

  /// 重新拍照
  Future<void> _retakePhoto() async {
    setState(() {
      _capturedImage = null;
    });
    await _takePhoto();
  }

  /// 提交照片
  void _submitPhoto() {
    if (_capturedImage != null) {
      // TODO: 上传照片到服务器
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('照片已提交'),
          backgroundColor: AppColors.emeraldGreen,
        ),
      );
    }
  }

  /// 在地图中打开
  void _openInMaps() {
    // TODO: 打开系统地图应用
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('地图功能开发中...'),
      ),
    );
  }
}
