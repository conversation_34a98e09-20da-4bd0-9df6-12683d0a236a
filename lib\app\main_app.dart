import 'package:flutter/material.dart';
import '../core/constants/app_colors.dart';
import '../core/constants/app_strings.dart';
import '../features/home/<USER>';
import '../features/map/map_screen.dart';
import '../features/achievements/achievement_screen.dart';
import '../shared/widgets/cute_button.dart';

/// 主应用界面
/// 包含底部导航的5个主要功能模块
class MainApp extends StatefulWidget {
  const MainApp({super.key});

  @override
  State<MainApp> createState() => _MainAppState();
}

class _MainAppState extends State<MainApp> {
  int _currentIndex = 0;
  
  // 页面列表
  final List<Widget> _pages = [
    const HomeScreen(),
    const MapScreen(),
    const InventoryScreen(),
    const AchievementScreen(),
    const MarketplaceScreen(),
    const ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: IndexedStack(
        index: _currentIndex,
        children: _pages,
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
      floatingActionButton: _buildFloatingActionButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }

  /// 构建底部导航栏
  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: const BoxDecoration(
        color: AppColors.cardWhite,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: _onTabTapped,
          type: BottomNavigationBarType.fixed,
          backgroundColor: Colors.transparent,
          elevation: 0,
          selectedItemColor: AppColors.primaryGold,
          unselectedItemColor: AppColors.textHint,
          selectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 12,
            fontFamily: 'PingFang SC',
          ),
          unselectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.normal,
            fontSize: 11,
            fontFamily: 'PingFang SC',
          ),
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.home_outlined),
              activeIcon: Icon(Icons.home),
              label: '🏠 首页',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.explore_outlined),
              activeIcon: Icon(Icons.explore),
              label: '🗺️ 探险地图',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.inventory_outlined),
              activeIcon: Icon(Icons.inventory),
              label: '🎒 背包仓库',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.emoji_events_outlined),
              activeIcon: Icon(Icons.emoji_events),
              label: '🏆 成就收藏',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.store_outlined),
              activeIcon: Icon(Icons.store),
              label: '🛒 交易市场',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.person_outline),
              activeIcon: Icon(Icons.person),
              label: '👤 个人中心',
            ),
          ],
        ),
      ),
    );
  }

  /// 构建浮动操作按钮
  Widget _buildFloatingActionButton() {
    return CuteFloatingActionButton(
      onPressed: _showQRScanner,
      icon: Icons.qr_code_scanner,
      tooltip: 'AR扫描',
      style: CuteButtonStyle.primary,
    );
  }

  /// 标签页点击处理
  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  /// 显示二维码扫描器
  void _showQRScanner() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 300,
        decoration: const BoxDecoration(
          color: AppColors.cardWhite,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: AppColors.border,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const Padding(
              padding: EdgeInsets.all(16),
              child: Text(
                '🔍 AR扫描功能',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'PingFang SC',
                ),
              ),
            ),
            const Expanded(
              child: Center(
                child: Text(
                  '扫描现实物品触发任务\n功能开发中...',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    color: AppColors.textSecondary,
                    fontFamily: 'PingFang SC',
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 背包仓库页面（占位）
class InventoryScreen extends StatelessWidget {
  const InventoryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: AppColors.backgroundGradient,
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          title: const Text('🎒 背包仓库'),
          backgroundColor: Colors.transparent,
        ),
        body: const Center(
          child: Text(
            '背包仓库功能开发中...\n敬请期待！',
            style: TextStyle(
              fontSize: 18,
              color: AppColors.textSecondary,
              fontFamily: 'PingFang SC',
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}

/// 成就收藏页面（占位）
class AchievementScreen extends StatelessWidget {
  const AchievementScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      appBar: AppBar(
        title: const Text('🏆 成就收藏'),
        backgroundColor: Colors.transparent,
      ),
      body: const Center(
        child: Text(
          '成就收藏功能开发中...',
          style: TextStyle(
            fontSize: 18,
            color: AppColors.textSecondary,
            fontFamily: 'PingFang SC',
          ),
        ),
      ),
    );
  }
}

/// 交易市场页面（占位）
class MarketplaceScreen extends StatelessWidget {
  const MarketplaceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      appBar: AppBar(
        title: const Text('🛒 交易市场'),
        backgroundColor: Colors.transparent,
      ),
      body: const Center(
        child: Text(
          '交易市场功能开发中...',
          style: TextStyle(
            fontSize: 18,
            color: AppColors.textSecondary,
            fontFamily: 'PingFang SC',
          ),
        ),
      ),
    );
  }
}

/// 个人中心页面（占位）
class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      appBar: AppBar(
        title: const Text('👤 个人中心'),
        backgroundColor: Colors.transparent,
      ),
      body: const Center(
        child: Text(
          '个人中心功能开发中...',
          style: TextStyle(
            fontSize: 18,
            color: AppColors.textSecondary,
            fontFamily: 'PingFang SC',
          ),
        ),
      ),
    );
  }
}
