{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/AndroidSDK/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/AndroidSDK/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/AndroidSDK/cmake/3.22.1/bin/ctest.exe", "root": "D:/AndroidSDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-04e0a28dcf99c6da51ad.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-c1e0f93654b197510b00.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-020ba672d177bb2bb36b.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-c1e0f93654b197510b00.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-020ba672d177bb2bb36b.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-04e0a28dcf99c6da51ad.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}