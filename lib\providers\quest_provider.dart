import 'package:flutter/foundation.dart';
import '../models/quest.dart';

/// 任务数据管理Provider
/// 管理任务列表、状态和进度
class QuestProvider extends ChangeNotifier {
  // 私有字段
  List<Quest> _availableQuests = [];
  List<Quest> _activeQuests = [];
  List<Quest> _completedQuests = [];
  bool _isLoading = false;
  String? _errorMessage;
  
  // 公共getter
  /// 可接受的任务列表
  List<Quest> get availableQuests => List.unmodifiable(_availableQuests);
  
  /// 进行中的任务列表
  List<Quest> get activeQuests => List.unmodifiable(_activeQuests);
  
  /// 已完成的任务列表
  List<Quest> get completedQuests => List.unmodifiable(_completedQuests);
  
  /// 是否正在加载
  bool get isLoading => _isLoading;
  
  /// 错误信息
  String? get errorMessage => _errorMessage;
  
  /// 今日完成的任务数量
  int get todayCompletedCount {
    final today = DateTime.now();
    return _completedQuests.where((quest) {
      final completedAt = quest.completedAt;
      return completedAt != null &&
          completedAt.year == today.year &&
          completedAt.month == today.month &&
          completedAt.day == today.day;
    }).length;
  }
  
  /// 初始化任务数据
  Future<void> initializeQuests() async {
    try {
      _setLoading(true);
      _clearError();
      
      // 模拟加载任务数据
      await Future.delayed(const Duration(milliseconds: 800));
      
      // 创建示例任务数据
      _generateSampleQuests();
      
      debugPrint('任务数据初始化完成');
      debugPrint('可接受任务: ${_availableQuests.length}');
      debugPrint('进行中任务: ${_activeQuests.length}');
      debugPrint('已完成任务: ${_completedQuests.length}');
    } catch (e) {
      _setError('加载任务数据失败: $e');
      debugPrint('任务数据初始化错误: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// 开始任务
  Future<void> startQuest(String questId) async {
    try {
      _setLoading(true);
      _clearError();
      
      // 查找任务
      final questIndex = _availableQuests.indexWhere((q) => q.id == questId);
      if (questIndex == -1) {
        throw Exception('任务不存在');
      }
      
      final quest = _availableQuests[questIndex];
      if (!quest.canStart) {
        throw Exception('任务无法开始');
      }
      
      // 模拟网络请求
      await Future.delayed(const Duration(milliseconds: 300));
      
      // 更新任务状态
      final updatedQuest = quest.copyWith(
        status: QuestStatus.inProgress,
        startedAt: DateTime.now(),
      );
      
      // 移动任务到进行中列表
      _availableQuests.removeAt(questIndex);
      _activeQuests.add(updatedQuest);
      
      debugPrint('开始任务: ${quest.title}');
    } catch (e) {
      _setError('开始任务失败: $e');
      debugPrint('开始任务错误: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// 完成任务
  Future<void> completeQuest(String questId) async {
    try {
      _setLoading(true);
      _clearError();
      
      // 查找任务
      final questIndex = _activeQuests.indexWhere((q) => q.id == questId);
      if (questIndex == -1) {
        throw Exception('任务不存在或未开始');
      }
      
      final quest = _activeQuests[questIndex];
      
      // 模拟网络请求
      await Future.delayed(const Duration(milliseconds: 300));
      
      // 更新任务状态
      final updatedQuest = quest.copyWith(
        status: QuestStatus.completed,
        completedAt: DateTime.now(),
      );
      
      // 移动任务到已完成列表
      _activeQuests.removeAt(questIndex);
      _completedQuests.add(updatedQuest);
      
      debugPrint('完成任务: ${quest.title}，获得经验: ${quest.experienceReward}');
    } catch (e) {
      _setError('完成任务失败: $e');
      debugPrint('完成任务错误: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// 刷新任务列表
  Future<void> refreshQuests() async {
    await initializeQuests();
  }
  
  /// 根据类型获取任务
  List<Quest> getQuestsByType(QuestType type) {
    final allQuests = [..._availableQuests, ..._activeQuests, ..._completedQuests];
    return allQuests.where((quest) => quest.type == type).toList();
  }
  
  /// 获取每日任务
  List<Quest> get dailyQuests {
    return getQuestsByType(QuestType.daily);
  }
  
  /// 清理数据
  void clear() {
    _availableQuests.clear();
    _activeQuests.clear();
    _completedQuests.clear();
    _isLoading = false;
    _errorMessage = null;
    notifyListeners();
  }
  
  // 私有方法
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }
  
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }
  
  void _clearError() {
    if (_errorMessage != null) {
      _errorMessage = null;
      notifyListeners();
    }
  }
  
  /// 生成示例任务数据
  void _generateSampleQuests() {
    final now = DateTime.now();
    
    // 可接受的任务
    _availableQuests = [
      Quest(
        id: 'quest_1',
        title: '探索附近公园',
        description: '前往最近的公园，拍摄一张风景照片',
        type: QuestType.exploration,
        status: QuestStatus.available,
        targetLatitude: 39.9042,
        targetLongitude: 116.4074,
        targetLocationName: '天安门广场',
        completionRadius: 100.0,
        experienceReward: 50,
        difficulty: QuestDifficulty.easy,
        estimatedDuration: 30,
        createdAt: now,
        requirements: '到达指定位置并拍摄照片',
      ),
      Quest(
        id: 'quest_2',
        title: '每日签到',
        description: '完成今日的签到任务',
        type: QuestType.daily,
        status: QuestStatus.available,
        completionRadius: 0.0,
        experienceReward: 20,
        difficulty: QuestDifficulty.easy,
        estimatedDuration: 5,
        createdAt: now,
        requirements: '点击签到按钮',
      ),
    ];
    
    // 进行中的任务
    _activeQuests = [
      Quest(
        id: 'quest_3',
        title: '收集城市地标',
        description: '拍摄5个不同的城市地标建筑',
        type: QuestType.collection,
        status: QuestStatus.inProgress,
        completionRadius: 50.0,
        experienceReward: 100,
        difficulty: QuestDifficulty.normal,
        estimatedDuration: 120,
        createdAt: now.subtract(const Duration(hours: 2)),
        startedAt: now.subtract(const Duration(hours: 1)),
        requirements: '拍摄5个不同地标的照片',
      ),
    ];
    
    // 已完成的任务
    _completedQuests = [
      Quest(
        id: 'quest_4',
        title: '新手教程',
        description: '完成游戏新手教程',
        type: QuestType.exploration,
        status: QuestStatus.completed,
        completionRadius: 0.0,
        experienceReward: 30,
        difficulty: QuestDifficulty.easy,
        estimatedDuration: 10,
        createdAt: now.subtract(const Duration(days: 1)),
        startedAt: now.subtract(const Duration(days: 1)),
        completedAt: now.subtract(const Duration(hours: 23)),
        requirements: '跟随教程完成所有步骤',
      ),
    ];
    
    notifyListeners();
  }
  
  @override
  void dispose() {
    clear();
    super.dispose();
  }
}
