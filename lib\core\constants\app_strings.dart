/// 应用文本常量定义
/// 为国际化做准备，所有用户可见文本都在此定义
class AppStrings {
  // 应用基础信息
  static const String appName = '宝藏奇缘';
  static const String appSubtitle = '现实秘境';
  
  // 底部导航
  static const String navHome = '首页';
  static const String navMap = '地图';
  static const String navAchievements = '成就';
  static const String navProfile = '我的';
  
  // 首页相关
  static const String homeWelcome = '欢迎回来，探险家！';
  static const String homeDailyQuest = '每日任务';
  static const String homeCurrentLevel = '当前等级';
  static const String homeExperience = '经验值';
  
  // 地图相关
  static const String mapTitle = '探险地图';
  static const String mapCurrentLocation = '当前位置';
  static const String mapNearbyQuests = '附近任务';
  
  // 成就相关
  static const String achievementsTitle = '成就收藏';
  static const String achievementsTotal = '总成就数';
  static const String achievementsCompleted = '已完成';
  static const String achievementsInProgress = '进行中';
  
  // 个人资料相关
  static const String profileTitle = '个人资料';
  static const String profileLevel = '等级';
  static const String profileExperience = '经验';
  static const String profileAchievements = '成就';
  static const String profileSettings = '设置';
  
  // 通用按钮
  static const String buttonStart = '开始';
  static const String buttonContinue = '继续';
  static const String buttonComplete = '完成';
  static const String buttonCancel = '取消';
  static const String buttonConfirm = '确认';
  static const String buttonRetry = '重试';
  
  // 状态信息
  static const String statusLoading = '加载中...';
  static const String statusDeveloping = '开发中...';
  static const String statusError = '出现错误';
  static const String statusSuccess = '操作成功';
  
  // 错误信息
  static const String errorNetwork = '网络连接失败，请检查网络设置';
  static const String errorLocation = '无法获取位置信息，请检查定位权限';
  static const String errorCamera = '无法访问相机，请检查相机权限';
  static const String errorGeneral = '操作失败，请重试';
  
  // 权限相关
  static const String permissionLocationTitle = '位置权限';
  static const String permissionLocationMessage = '应用需要位置权限来提供基于位置的游戏体验';
  static const String permissionCameraTitle = '相机权限';
  static const String permissionCameraMessage = '应用需要相机权限来拍摄任务照片';
  static const String permissionDenied = '权限被拒绝';
  static const String permissionSettings = '前往设置';
  
  // 私有构造函数，防止实例化
  AppStrings._();
}
