import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_sizes.dart';

/// 游戏化进度条组件
/// 提供多种游戏风格的进度条样式
class GameProgressBar extends StatefulWidget {
  final double value;
  final String? label;
  final String? currentText;
  final String? maxText;
  final GameProgressStyle style;
  final double height;
  final bool showPercentage;
  final bool animated;
  final Duration animationDuration;

  const GameProgressBar({
    super.key,
    required this.value,
    this.label,
    this.currentText,
    this.maxText,
    this.style = GameProgressStyle.experience,
    this.height = 24,
    this.showPercentage = false,
    this.animated = true,
    this.animationDuration = const Duration(milliseconds: 800),
  });

  @override
  State<GameProgressBar> createState() => _GameProgressBarState();
}

class _GameProgressBarState extends State<GameProgressBar>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _progressAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: widget.value.clamp(0.0, 1.0),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _glowAnimation = Tween<double>(
      begin: 0.3,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (widget.animated) {
      _animationController.forward();
    }
  }

  @override
  void didUpdateWidget(GameProgressBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _progressAnimation = Tween<double>(
        begin: oldWidget.value.clamp(0.0, 1.0),
        end: widget.value.clamp(0.0, 1.0),
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ));
      if (widget.animated) {
        _animationController.forward(from: 0);
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // 标签和数值
        if (widget.label != null || widget.currentText != null || widget.maxText != null)
          Padding(
            padding: const EdgeInsets.only(bottom: AppSizes.spacing8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (widget.label != null)
                  Text(
                    widget.label!,
                    style: TextStyle(
                      color: AppColors.starSilver,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                if (widget.currentText != null && widget.maxText != null)
                  Text(
                    '${widget.currentText}/${widget.maxText}',
                    style: TextStyle(
                      color: _getProgressColor(),
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                else if (widget.showPercentage)
                  Text(
                    '${(widget.value * 100).toInt()}%',
                    style: TextStyle(
                      color: _getProgressColor(),
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
              ],
            ),
          ),
        
        // 进度条
        AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            final progress = widget.animated ? _progressAnimation.value : widget.value;
            return Container(
              height: widget.height,
              decoration: BoxDecoration(
                color: AppColors.starGray.withOpacity(0.2),
                borderRadius: BorderRadius.circular(widget.height / 2),
                border: Border.all(
                  color: AppColors.starGray.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Stack(
                children: [
                  // 背景轨道
                  Container(
                    height: widget.height,
                    decoration: BoxDecoration(
                      color: AppColors.starGray.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(widget.height / 2),
                    ),
                  ),
                  
                  // 进度填充
                  FractionallySizedBox(
                    widthFactor: progress,
                    child: Container(
                      height: widget.height,
                      decoration: BoxDecoration(
                        gradient: _getProgressGradient(),
                        borderRadius: BorderRadius.circular(widget.height / 2),
                        boxShadow: [
                          BoxShadow(
                            color: _getProgressColor().withOpacity(0.4),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  // 光效
                  if (progress > 0)
                    FractionallySizedBox(
                      widthFactor: progress,
                      child: Container(
                        height: widget.height,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.white.withOpacity(0.0),
                              Colors.white.withOpacity(0.3),
                              Colors.white.withOpacity(0.0),
                            ],
                            stops: const [0.0, 0.5, 1.0],
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                          ),
                          borderRadius: BorderRadius.circular(widget.height / 2),
                        ),
                      ),
                    ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  /// 获取进度条颜色
  Color _getProgressColor() {
    switch (widget.style) {
      case GameProgressStyle.experience:
        return AppColors.treasureGold;
      case GameProgressStyle.health:
        return AppColors.flameRed;
      case GameProgressStyle.mana:
        return AppColors.magicPurple;
      case GameProgressStyle.energy:
        return AppColors.energyBlue;
      case GameProgressStyle.achievement:
        return AppColors.emeraldGreen;
      case GameProgressStyle.quest:
        return AppColors.adventureOrange;
    }
  }

  /// 获取进度条渐变
  LinearGradient _getProgressGradient() {
    switch (widget.style) {
      case GameProgressStyle.experience:
        return AppColors.treasureGradient;
      case GameProgressStyle.health:
        return LinearGradient(
          colors: [AppColors.flameRed, AppColors.flameRed.withOpacity(0.8)],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        );
      case GameProgressStyle.mana:
        return AppColors.magicGradient;
      case GameProgressStyle.energy:
        return LinearGradient(
          colors: [AppColors.energyBlue, AppColors.neonCyan],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        );
      case GameProgressStyle.achievement:
        return AppColors.successGradient;
      case GameProgressStyle.quest:
        return AppColors.adventureGradient;
    }
  }
}

/// 游戏进度条样式枚举
enum GameProgressStyle {
  /// 经验值进度条 - 金色
  experience,
  
  /// 生命值进度条 - 红色
  health,
  
  /// 魔法值进度条 - 紫色
  mana,
  
  /// 能量值进度条 - 蓝色
  energy,
  
  /// 成就进度条 - 青色
  achievement,
  
  /// 任务进度条 - 橙色
  quest,
}
